(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6856],{6050:(e,a,t)=>{"use strict";t.d(a,{$n:()=>i,JR:()=>c,T6:()=>r,Zu:()=>l,qR:()=>s});let l="G-X8ZRRZX2EQ",c=e=>{try{window.gtag&&window.gtag("config",l,{page_path:e})}catch(e){}},n=e=>{let{action:a,category:t,label:l,value:c}=e;window.gtag&&window.gtag("event",a,{event_category:t,event_label:l,value:c})},r=(e,a)=>{n({action:"click_ad",category:"ads",label:"".concat(e," - ").concat(a)})},i=e=>{n({action:"newsletter_subscribe",category:"engagement",label:e})},s=e=>{n({action:"page_scroll",category:"engagement",label:"".concat(e,"%"),value:e})}},6736:(e,a,t)=>{"use strict";t.d(a,{default:()=>o});var l=t(5155),c=t(2115),n=t(6766),r=t(6874),i=t.n(r),s=t(6050);function o(e){let{placement:a,className:t}=e,[r,o]=(0,c.useState)(null),[d,u]=(0,c.useState)(!0);(0,c.useEffect)(()=>{(async()=>{u(!0);try{let e=await fetch("/api/ads?type=banner&placement=".concat(a,"&is_active=true&limit=1"));if(e.ok){let a=await e.json();a.ads&&a.ads.length>0?o(a.ads[0]):o(null)}else o(null)}catch(e){o(null)}u(!1)})()},[a]);let g=async()=>{if(r)try{await fetch("/api/ads/".concat(r.id,"/click"),{method:"POST"}),(0,s.T6)(r.title,a)}catch(e){console.error("Failed to track ad click:",e)}};return d?(0,l.jsx)("div",{className:"w-full h-24 bg-dark-card animate-pulse rounded-lg my-8 ".concat(t||"")}):r&&r.image_url?(0,l.jsx)("div",{className:"my-8 ".concat(t||""),children:(0,l.jsx)(i(),{href:r.link_url||"#",target:"_blank",rel:"noopener noreferrer",onClick:g,children:(0,l.jsx)("div",{className:"relative w-full h-auto aspect-[8/1] bg-dark-card rounded-lg overflow-hidden",children:(0,l.jsx)(n.default,{src:r.image_url,alt:r.title,fill:!0,style:{objectFit:"cover"},priority:!0})})})}):null}},9141:(e,a,t)=>{Promise.resolve().then(t.t.bind(t,6874,23)),Promise.resolve().then(t.bind(t,6736))}},e=>{var a=a=>e(e.s=a);e.O(0,[6874,6766,8441,1684,7358],()=>a(9141)),_N_E=e.O()}]);