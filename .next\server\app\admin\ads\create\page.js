(()=>{var e={};e.id=2264,e.ids=[2264],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,t,r)=>{"use strict";r.d(t,{ProtectedRoute:()=>l});var s=r(60687),a=r(63213),i=r(16189);function l({children:e}){let{user:t,loading:r}=(0,a.A)();return((0,i.useRouter)(),r)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"جاري التحقق من صلاحيات الوصول..."})]})}):t?(0,s.jsx)(s.Fragment,{children:e}):null}r(43210)},25518:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(60687),a=r(43210),i=r(16189),l=r(16391),n=r(37590);function o(){(0,i.useRouter)();let[e,t]=(0,a.useState)([]),[r,o]=(0,a.useState)(!0),[d,c]=(0,a.useState)(!1),[u,x]=(0,a.useState)(null),[m,p]=(0,a.useState)({title:"",content:"",type:"banner",position:"header",target_url:"",image_url:"",video_url:"",custom_css:"",custom_js:"",priority:1,max_views:"",max_clicks:"",start_date:"",end_date:"",is_active:!0}),h=async()=>{try{let{data:e,error:r}=await l.ND.from("advertisements").select("*").order("created_at",{ascending:!1});if(r)throw r;t(e||[])}catch(e){console.error("Error loading ads:",e),n.Ay.error("خطأ في تحميل الإعلانات")}finally{o(!1)}},g=()=>{p({title:"",content:"",type:"banner",position:"header",target_url:"",image_url:"",video_url:"",custom_css:"",custom_js:"",priority:1,max_views:"",max_clicks:"",start_date:"",end_date:"",is_active:!0}),x(null),c(!1)},b=async e=>{if(e.preventDefault(),!m.title.trim()||!m.content.trim())return void n.Ay.error("العنوان والمحتوى مطلوبان");try{let e={title:m.title.trim(),content:m.content.trim(),type:m.type,position:m.position,target_url:m.target_url.trim()||null,image_url:m.image_url.trim()||null,video_url:m.video_url.trim()||null,custom_css:m.custom_css.trim()||null,custom_js:m.custom_js.trim()||null,priority:m.priority,max_views:m.max_views?parseInt(m.max_views):null,max_clicks:m.max_clicks?parseInt(m.max_clicks):null,start_date:m.start_date||null,end_date:m.end_date||null,is_active:m.is_active};if(u){let{error:t}=await l.ND.from("advertisements").update(e).eq("id",u.id);if(t)throw t;n.Ay.success("تم تحديث الإعلان بنجاح")}else{let{error:t}=await l.ND.from("advertisements").insert([e]);if(t)throw t;n.Ay.success("تم إنشاء الإعلان بنجاح")}g(),h()}catch(e){console.error("Error saving ad:",e),n.Ay.error("حدث خطأ في حفظ الإعلان")}},v=e=>{p({title:e.title,content:e.content,type:e.type,position:e.position,target_url:e.target_url||"",image_url:e.image_url||"",video_url:e.video_url||"",custom_css:e.custom_css||"",custom_js:e.custom_js||"",priority:e.priority,max_views:e.max_views?.toString()||"",max_clicks:e.max_clicks?.toString()||"",start_date:e.start_date?e.start_date.split("T")[0]:"",end_date:e.end_date?e.end_date.split("T")[0]:"",is_active:e.is_active}),x(e),c(!0)},y=async e=>{if(confirm("هل أنت متأكد من حذف هذا الإعلان؟"))try{let{error:t}=await l.ND.from("advertisements").delete().eq("id",e);if(t)throw t;n.Ay.success("تم حذف الإعلان بنجاح"),h()}catch(e){console.error("Error deleting ad:",e),n.Ay.error("حدث خطأ في حذف الإعلان")}},f=async(e,t)=>{try{let{error:r}=await l.ND.from("advertisements").update({is_active:!t}).eq("id",e);if(r)throw r;n.Ay.success(`تم ${!t?"تفعيل":"إيقاف"} الإعلان`),h()}catch(e){console.error("Error toggling ad status:",e),n.Ay.error("حدث خطأ في تغيير حالة الإعلان")}},j=[{value:"banner",label:"بانر"},{value:"popup",label:"منبثق"},{value:"sidebar",label:"جانبي"},{value:"text",label:"نصي"},{value:"image",label:"صورة"},{value:"video",label:"فيديو"},{value:"html",label:"HTML مخصص"},{value:"adsense",label:"Google AdSense"}],_=[{value:"header",label:"أعلى الصفحة"},{value:"footer",label:"أسفل الصفحة"},{value:"sidebar-right",label:"الشريط الجانبي الأيمن"},{value:"sidebar-left",label:"الشريط الجانبي الأيسر"},{value:"in-content",label:"داخل المحتوى"},{value:"popup",label:"نافذة منبثقة"},{value:"floating",label:"عائم"}];return r?(0,s.jsx)("div",{className:"min-h-screen bg-dark-background flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-white",children:"جاري التحميل..."})}):(0,s.jsx)("div",{className:"min-h-screen bg-dark-background",children:(0,s.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"إدارة الإعلانات"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"إنشاء وإدارة الإعلانات في الموقع"})]}),(0,s.jsxs)("button",{onClick:()=>c(!0),className:"flex items-center space-x-2 space-x-reverse bg-primary text-white px-6 py-3 rounded-lg hover:bg-primary/90 transition-colors",children:[(0,s.jsx)("span",{children:"+"}),(0,s.jsx)("span",{children:"إعلان جديد"})]})]}),d&&(0,s.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800 mb-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-6",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white",children:u?"تعديل الإعلان":"إنشاء إعلان جديد"}),(0,s.jsx)("button",{onClick:g,className:"text-gray-400 hover:text-white",children:"✕"})]}),(0,s.jsxs)("form",{onSubmit:b,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"عنوان الإعلان *"}),(0,s.jsx)("input",{type:"text",value:m.title,onChange:e=>p(t=>({...t,title:e.target.value})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"أدخل عنوان الإعلان",required:!0})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"نوع الإعلان"}),(0,s.jsx)("select",{value:m.type,onChange:e=>p(t=>({...t,type:e.target.value})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent",children:j.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"موقع الإعلان"}),(0,s.jsx)("select",{value:m.position,onChange:e=>p(t=>({...t,position:e.target.value})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent",children:_.map(e=>(0,s.jsx)("option",{value:e.value,children:e.label},e.value))})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"الأولوية (1-10)"}),(0,s.jsx)("input",{type:"number",min:"1",max:"10",value:m.priority,onChange:e=>p(t=>({...t,priority:parseInt(e.target.value)||1})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent"})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"محتوى الإعلان *"}),(0,s.jsx)("textarea",{value:m.content,onChange:e=>p(t=>({...t,content:e.target.value})),rows:6,className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent font-mono",placeholder:"أدخل محتوى الإعلان (HTML, نص، كود AdSense، إلخ)",required:!0})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"رابط الهدف"}),(0,s.jsx)("input",{type:"url",value:m.target_url,onChange:e=>p(t=>({...t,target_url:e.target.value})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"https://example.com"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"رابط الصورة"}),(0,s.jsx)("input",{type:"url",value:m.image_url,onChange:e=>p(t=>({...t,image_url:e.target.value})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"https://example.com/image.jpg"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"تاريخ البداية"}),(0,s.jsx)("input",{type:"date",value:m.start_date,onChange:e=>p(t=>({...t,start_date:e.target.value})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"تاريخ النهاية"}),(0,s.jsx)("input",{type:"date",value:m.end_date,onChange:e=>p(t=>({...t,end_date:e.target.value})),className:"w-full px-4 py-3 bg-dark-background border border-gray-700 rounded-lg text-white focus:ring-2 focus:ring-primary focus:border-transparent"})]})]}),(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("label",{className:"flex items-center",children:[(0,s.jsx)("input",{type:"checkbox",checked:m.is_active,onChange:e=>p(t=>({...t,is_active:e.target.checked})),className:"w-4 h-4 text-primary bg-dark-background border-gray-700 rounded focus:ring-primary"}),(0,s.jsx)("span",{className:"mr-2 text-dark-text",children:"إعلان نشط"})]})}),m.content&&(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-dark-text mb-2",children:"معاينة الإعلان"}),(0,s.jsx)("div",{className:"border border-gray-700 rounded-lg p-4 bg-gray-800/50",children:(0,s.jsx)("div",{dangerouslySetInnerHTML:{__html:m.content}})})]}),(0,s.jsxs)("div",{className:"flex justify-end space-x-4 space-x-reverse",children:[(0,s.jsx)("button",{type:"button",onClick:g,className:"px-6 py-3 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"إلغاء"}),(0,s.jsx)("button",{type:"submit",className:"px-6 py-3 bg-primary text-white rounded-lg hover:bg-primary/90 transition-colors",children:u?"تحديث الإعلان":"إنشاء الإعلان"})]})]})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-xl border border-gray-800",children:[(0,s.jsx)("div",{className:"p-6 border-b border-gray-800",children:(0,s.jsx)("h2",{className:"text-xl font-semibold text-white",children:"الإعلانات الحالية"})}),0===e.length?(0,s.jsx)("div",{className:"p-8 text-center text-gray-500",children:"لا توجد إعلانات حالياً"}):(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-gray-800",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"العنوان"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"النوع"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"الموقع"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"الحالة"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-300 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{className:"divide-y divide-gray-800",children:e.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-800/50",children:[(0,s.jsxs)("td",{className:"px-6 py-4 whitespace-nowrap",children:[(0,s.jsx)("div",{className:"text-sm font-medium text-white",children:e.title}),(0,s.jsxs)("div",{className:"text-sm text-gray-400 truncate max-w-xs",children:[e.content.replace(/<[^>]*>/g,"").substring(0,50),"..."]})]}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium bg-gray-700 text-gray-300 rounded",children:j.find(t=>t.value===e.type)?.label||e.type})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("span",{className:"px-2 py-1 text-xs font-medium bg-gray-700 text-gray-300 rounded",children:_.find(t=>t.value===e.position)?.label||e.position})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("button",{onClick:()=>f(e.id,e.is_active),className:`px-2 py-1 text-xs font-medium rounded ${e.is_active?"bg-green-600 text-white":"bg-red-600 text-white"}`,children:e.is_active?"نشط":"متوقف"})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,s.jsx)("button",{onClick:()=>v(e),className:"text-primary hover:text-primary/80",children:"تعديل"}),(0,s.jsx)("button",{onClick:()=>y(e.id),className:"text-red-400 hover:text-red-300",children:"حذف"})]})})]},e.id))})]})})]})]})})})}},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},30967:(e,t,r)=>{Promise.resolve().then(r.bind(r,25518))},33134:(e,t,r)=>{Promise.resolve().then(r.bind(r,67083))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},36543:(e,t,r)=>{Promise.resolve().then(r.bind(r,66132))},50813:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>l.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>d});var s=r(65239),a=r(48088),i=r(88170),l=r.n(i),n=r(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);r.d(t,o);let d={children:["",{children:["admin",{children:["ads",{children:["create",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,66132)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\ads\\create\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\4\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\4\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\4\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\4\\src\\app\\admin\\ads\\create\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/ads/create/page",pathname:"/admin/ads/create",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},66132:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\admin\\\\ads\\\\create\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\app\\admin\\ads\\create\\page.tsx","default")},67083:(e,t,r)=>{"use strict";r.d(t,{ProtectedRoute:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96182:(e,t,r)=>{Promise.resolve().then(r.bind(r,20769))},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),a=r(67083);function i({children:e}){return(0,s.jsx)(a.ProtectedRoute,{children:e})}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2714,6656],()=>r(50813));module.exports=s})();