<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>غير متصل - TechnoFlash</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #0D1117 0%, #161B22 100%);
            color: #E6EDF3;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            width: 100%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #38BDF8 0%, #3B82F6 100%);
            border-radius: 16px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 30px;
            font-size: 32px;
            font-weight: bold;
            color: white;
            box-shadow: 0 10px 30px rgba(56, 189, 248, 0.3);
        }
        
        h1 {
            font-size: 2.5rem;
            margin-bottom: 20px;
            background: linear-gradient(135deg, #38BDF8, #3B82F6);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        p {
            font-size: 1.2rem;
            line-height: 1.6;
            margin-bottom: 30px;
            color: #8B949E;
        }
        
        .offline-icon {
            width: 120px;
            height: 120px;
            margin: 0 auto 30px;
            opacity: 0.7;
        }
        
        .retry-btn {
            background: linear-gradient(135deg, #38BDF8, #3B82F6);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 12px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            box-shadow: 0 5px 15px rgba(56, 189, 248, 0.3);
            margin: 10px;
        }
        
        .retry-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(56, 189, 248, 0.4);
        }
        
        .features {
            margin-top: 40px;
            text-align: right;
        }
        
        .feature {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 15px;
            background: rgba(22, 27, 34, 0.5);
            border-radius: 10px;
            border: 1px solid rgba(56, 189, 248, 0.1);
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            margin-left: 15px;
            color: #38BDF8;
        }
        
        .status {
            margin-top: 30px;
            padding: 15px;
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid rgba(239, 68, 68, 0.3);
            border-radius: 10px;
            color: #FCA5A5;
        }
        
        @media (max-width: 768px) {
            h1 {
                font-size: 2rem;
            }
            
            p {
                font-size: 1rem;
            }
            
            .retry-btn {
                padding: 12px 24px;
                font-size: 1rem;
            }
        }
        
        .pulse {
            animation: pulse 2s infinite;
        }
        
        @keyframes pulse {
            0% {
                opacity: 1;
            }
            50% {
                opacity: 0.5;
            }
            100% {
                opacity: 1;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">T</div>
        
        <div class="offline-icon pulse">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
                <polyline points="9,22 9,12 15,12 15,22"/>
                <line x1="1" y1="1" x2="23" y2="23"/>
            </svg>
        </div>
        
        <h1>غير متصل بالإنترنت</h1>
        
        <p>
            يبدو أنك غير متصل بالإنترنت حالياً. تحقق من اتصالك وحاول مرة أخرى.
        </p>
        
        <div class="status">
            <strong>حالة الاتصال:</strong> غير متصل
        </div>
        
        <button class="retry-btn" onclick="location.reload()">
            إعادة المحاولة
        </button>
        
        <button class="retry-btn" onclick="goHome()">
            العودة للرئيسية
        </button>
        
        <div class="features">
            <h3 style="margin-bottom: 20px; color: #38BDF8;">ما يمكنك فعله:</h3>
            
            <div class="feature">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M9 19c-5 0-8-3-8-8s3-8 8-8 8 3 8 8-3 8-8 8z"/>
                    <path d="M15 12h6m-3-3v6"/>
                </svg>
                <div>
                    <strong>تحقق من اتصال الإنترنت</strong><br>
                    تأكد من أن جهازك متصل بالإنترنت
                </div>
            </div>
            
            <div class="feature">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
                    <circle cx="12" cy="12" r="3"/>
                </svg>
                <div>
                    <strong>تصفح المحتوى المحفوظ</strong><br>
                    بعض الصفحات قد تكون متاحة دون اتصال
                </div>
            </div>
            
            <div class="feature">
                <svg class="feature-icon" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <path d="M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z"/>
                </svg>
                <div>
                    <strong>تابعنا على وسائل التواصل</strong><br>
                    ابق على اطلاع بآخر التحديثات
                </div>
            </div>
        </div>
    </div>
    
    <script>
        function goHome() {
            window.location.href = '/';
        }
        
        // تحديث حالة الاتصال
        function updateConnectionStatus() {
            const status = document.querySelector('.status');
            if (navigator.onLine) {
                status.innerHTML = '<strong>حالة الاتصال:</strong> متصل';
                status.style.background = 'rgba(34, 197, 94, 0.1)';
                status.style.borderColor = 'rgba(34, 197, 94, 0.3)';
                status.style.color = '#86EFAC';
                
                // إعادة تحميل الصفحة تلقائياً عند عودة الاتصال
                setTimeout(() => {
                    location.reload();
                }, 1000);
            } else {
                status.innerHTML = '<strong>حالة الاتصال:</strong> غير متصل';
                status.style.background = 'rgba(239, 68, 68, 0.1)';
                status.style.borderColor = 'rgba(239, 68, 68, 0.3)';
                status.style.color = '#FCA5A5';
            }
        }
        
        // مراقبة تغيير حالة الاتصال
        window.addEventListener('online', updateConnectionStatus);
        window.addEventListener('offline', updateConnectionStatus);
        
        // تحديث الحالة عند تحميل الصفحة
        updateConnectionStatus();
        
        // محاولة إعادة الاتصال كل 30 ثانية
        setInterval(() => {
            if (navigator.onLine) {
                location.reload();
            }
        }, 30000);
    </script>
</body>
</html>
