globalThis.__RSC_MANIFEST=(globalThis.__RSC_MANIFEST||{});globalThis.__RSC_MANIFEST["/admin/page"]={"moduleLoading":{"prefix":"/_next/"},"ssrModuleMapping":{"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(ssr)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AccessibilityHelper.tsx":{"*":{"id":"(ssr)/./src/components/AccessibilityHelper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx":{"*":{"id":"(ssr)/./src/components/ads/TechnoFlashBanner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AdSenseScript.tsx":{"*":{"id":"(ssr)/./src/components/AdSenseScript.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/GoogleAnalytics.tsx":{"*":{"id":"(ssr)/./src/components/GoogleAnalytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(ssr)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HydrationFix.tsx":{"*":{"id":"(ssr)/./src/components/HydrationFix.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HydrationSafeWrapper.tsx":{"*":{"id":"(ssr)/./src/components/HydrationSafeWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/PerformanceMonitor.tsx":{"*":{"id":"(ssr)/./src/components/PerformanceMonitor.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ScrollTracker.tsx":{"*":{"id":"(ssr)/./src/components/ScrollTracker.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(ssr)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ads/AdBanner.tsx":{"*":{"id":"(ssr)/./src/components/ads/AdBanner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ServicesSection.tsx":{"*":{"id":"(ssr)/./src/components/ServicesSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ProtectedRoute.tsx":{"*":{"id":"(ssr)/./src/components/ProtectedRoute.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(ssr)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/articles/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/articles/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/articles/create/page.tsx":{"*":{"id":"(ssr)/./src/app/admin/articles/create/page.tsx","name":"*","chunks":[],"async":false}}},"edgeSSRModuleMapping":{},"clientModules":{"C:\\Users\\<USER>\\4\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\font\\google\\target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\"],\"weight\":[\"400\",\"700\"],\"variable\":\"--font-tajawal\"}],\"variableName\":\"tajawal\"}":{"id":"(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Tajawal\",\"arguments\":[{\"subsets\":[\"arabic\"],\"weight\":[\"400\",\"700\"],\"variable\":\"--font-tajawal\"}],\"variableName\":\"tajawal\"}","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\react-hot-toast\\dist\\index.mjs":{"id":"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\app\\globals.css":{"id":"(app-pages-browser)/./src/app/globals.css","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\AccessibilityHelper.tsx":{"id":"(app-pages-browser)/./src/components/AccessibilityHelper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\ads\\TechnoFlashBanner.tsx":{"id":"(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\AdSenseScript.tsx":{"id":"(app-pages-browser)/./src/components/AdSenseScript.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\GoogleAnalytics.tsx":{"id":"(app-pages-browser)/./src/components/GoogleAnalytics.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\Header.tsx":{"id":"(app-pages-browser)/./src/components/Header.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\HydrationFix.tsx":{"id":"(app-pages-browser)/./src/components/HydrationFix.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\HydrationSafeWrapper.tsx":{"id":"(app-pages-browser)/./src/components/HydrationSafeWrapper.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\PerformanceMonitor.tsx":{"id":"(app-pages-browser)/./src/components/PerformanceMonitor.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\ScrollTracker.tsx":{"id":"(app-pages-browser)/./src/components/ScrollTracker.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\contexts\\AuthContext.tsx":{"id":"(app-pages-browser)/./src/contexts/AuthContext.tsx","name":"*","chunks":["app/layout","static/chunks/app/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\app-dir\\link.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":["app/not-found","static/chunks/app/not-found.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\client-page.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\client-segment.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\http-access-fallback\\error-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\layout-router.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\async-metadata.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\metadata\\metadata-boundary.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\node_modules\\next\\dist\\esm\\client\\components\\render-from-template-context.js":{"id":"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":["app-pages-internals","static/chunks/app-pages-internals.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\ads\\AdBanner.tsx":{"id":"(app-pages-browser)/./src/components/ads/AdBanner.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\ServicesSection.tsx":{"id":"(app-pages-browser)/./src/components/ServicesSection.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\4\\src\\components\\ProtectedRoute.tsx":{"id":"(app-pages-browser)/./src/components/ProtectedRoute.tsx","name":"*","chunks":["app/admin/layout","static/chunks/app/admin/layout.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\app\\admin\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/page.tsx","name":"*","chunks":["app/admin/page","static/chunks/app/admin/page.js"],"async":false},"C:\\Users\\<USER>\\4\\src\\app\\login\\page.tsx":{"id":"(app-pages-browser)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\4\\src\\app\\admin\\articles\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/articles/page.tsx","name":"*","chunks":[],"async":false},"C:\\Users\\<USER>\\4\\src\\app\\admin\\articles\\create\\page.tsx":{"id":"(app-pages-browser)/./src/app/admin/articles/create/page.tsx","name":"*","chunks":[],"async":false}},"entryCSSFiles":{"C:\\Users\\<USER>\\4\\src\\":[],"C:\\Users\\<USER>\\4\\src\\app\\layout":[{"inlined":false,"path":"static/css/app/layout.css"}],"C:\\Users\\<USER>\\4\\src\\app\\loading":[],"C:\\Users\\<USER>\\4\\src\\app\\not-found":[],"C:\\Users\\<USER>\\4\\src\\app\\admin\\layout":[],"C:\\Users\\<USER>\\4\\src\\app\\admin\\page":[]},"rscModuleMapping":{"(app-pages-browser)/./node_modules/react-hot-toast/dist/index.mjs":{"*":{"id":"(rsc)/./node_modules/react-hot-toast/dist/index.mjs","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/globals.css":{"*":{"id":"(rsc)/./src/app/globals.css","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AccessibilityHelper.tsx":{"*":{"id":"(rsc)/./src/components/AccessibilityHelper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ads/TechnoFlashBanner.tsx":{"*":{"id":"(rsc)/./src/components/ads/TechnoFlashBanner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/AdSenseScript.tsx":{"*":{"id":"(rsc)/./src/components/AdSenseScript.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/GoogleAnalytics.tsx":{"*":{"id":"(rsc)/./src/components/GoogleAnalytics.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/Header.tsx":{"*":{"id":"(rsc)/./src/components/Header.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HydrationFix.tsx":{"*":{"id":"(rsc)/./src/components/HydrationFix.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/HydrationSafeWrapper.tsx":{"*":{"id":"(rsc)/./src/components/HydrationSafeWrapper.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/PerformanceMonitor.tsx":{"*":{"id":"(rsc)/./src/components/PerformanceMonitor.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ScrollTracker.tsx":{"*":{"id":"(rsc)/./src/components/ScrollTracker.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/contexts/AuthContext.tsx":{"*":{"id":"(rsc)/./src/contexts/AuthContext.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/app-dir/link.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-page.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-page.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/client-segment.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/client-segment.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/http-access-fallback/error-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/layout-router.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/layout-router.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/async-metadata.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/async-metadata.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/metadata/metadata-boundary.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./node_modules/next/dist/client/components/render-from-template-context.js":{"*":{"id":"(rsc)/./node_modules/next/dist/client/components/render-from-template-context.js","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ads/AdBanner.tsx":{"*":{"id":"(rsc)/./src/components/ads/AdBanner.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ServicesSection.tsx":{"*":{"id":"(rsc)/./src/components/ServicesSection.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/components/ProtectedRoute.tsx":{"*":{"id":"(rsc)/./src/components/ProtectedRoute.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/login/page.tsx":{"*":{"id":"(rsc)/./src/app/login/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/articles/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/articles/page.tsx","name":"*","chunks":[],"async":false}},"(app-pages-browser)/./src/app/admin/articles/create/page.tsx":{"*":{"id":"(rsc)/./src/app/admin/articles/create/page.tsx","name":"*","chunks":[],"async":false}}},"edgeRscModuleMapping":{}}