"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/articles/page",{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAITool: () => (/* binding */ createAITool),\n/* harmony export */   createArticle: () => (/* binding */ createArticle),\n/* harmony export */   createService: () => (/* binding */ createService),\n/* harmony export */   deleteAITool: () => (/* binding */ deleteAITool),\n/* harmony export */   deleteArticle: () => (/* binding */ deleteArticle),\n/* harmony export */   deleteService: () => (/* binding */ deleteService),\n/* harmony export */   getAIToolById: () => (/* binding */ getAIToolById),\n/* harmony export */   getAITools: () => (/* binding */ getAITools),\n/* harmony export */   getArticleById: () => (/* binding */ getArticleById),\n/* harmony export */   getArticles: () => (/* binding */ getArticles),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getServiceById: () => (/* binding */ getServiceById),\n/* harmony export */   getServices: () => (/* binding */ getServices),\n/* harmony export */   updateAITool: () => (/* binding */ updateAITool),\n/* harmony export */   updateArticle: () => (/* binding */ updateArticle),\n/* harmony export */   updateService: () => (/* binding */ updateService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _cache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cache */ \"(app-pages-browser)/./src/lib/cache.ts\");\n// مساعدات قاعدة البيانات لعمليات CRUD\n\n\n// ===== مساعدات المقالات =====\n// جلب جميع المقالات مع التخزين المؤقت - محسن لتوفير Egress\nasync function getArticles() {\n    return (0,_cache__WEBPACK_IMPORTED_MODULE_1__.cachedQuery)('articles-all', async ()=>{\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').select(\"\\n        id,\\n        title,\\n        slug,\\n        excerpt,\\n        featured_image_url,\\n        published_at,\\n        created_at,\\n        status\\n      \").eq('status', 'published').order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            throw new Error(\"خطأ في جلب المقالات: \".concat(error.message));\n        }\n        return data;\n    }, 1800); // 30 دقيقة بدلاً من 5 دقائق\n}\n// جلب مقال واحد بالـ ID\nasync function getArticleById(id) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').select('*').eq('id', id).single();\n    if (error) {\n        throw new Error(\"خطأ في جلب المقال: \".concat(error.message));\n    }\n    return data;\n}\n// إنشاء مقال جديد\nasync function createArticle(articleData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').insert([\n        {\n            ...articleData,\n            published_at: articleData.status === 'published' ? new Date().toISOString() : null\n        }\n    ]).select().single();\n    if (error) {\n        throw new Error(\"خطأ في إنشاء المقال: \".concat(error.message));\n    }\n    return data;\n}\n// تحديث مقال\nasync function updateArticle(id, articleData) {\n    const updateData = {\n        ...articleData,\n        updated_at: new Date().toISOString()\n    };\n    // إذا تم تغيير الحالة إلى منشور، تحديث تاريخ النشر\n    if (articleData.status === 'published') {\n        updateData.published_at = new Date().toISOString();\n    } else if (articleData.status === 'draft') {\n        updateData.published_at = null;\n    }\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').update(updateData).eq('id', id).select().single();\n    if (error) {\n        throw new Error(\"خطأ في تحديث المقال: \".concat(error.message));\n    }\n    return data;\n}\n// حذف مقال\nasync function deleteArticle(id) {\n    const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').delete().eq('id', id);\n    if (error) {\n        throw new Error(\"خطأ في حذف المقال: \".concat(error.message));\n    }\n}\n// ===== مساعدات أدوات الذكاء الاصطناعي =====\n// جلب جميع أدوات الذكاء الاصطناعي\nasync function getAITools() {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').select('*').order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        throw new Error(\"خطأ في جلب أدوات الذكاء الاصطناعي: \".concat(error.message));\n    }\n    return data;\n}\n// جلب أداة ذكاء اصطناعي واحدة بالـ ID\nasync function getAIToolById(id) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').select('*').eq('id', id).single();\n    if (error) {\n        throw new Error(\"خطأ في جلب أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n    return data;\n}\n// إنشاء أداة ذكاء اصطناعي جديدة\nasync function createAITool(toolData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').insert([\n        toolData\n    ]).select().single();\n    if (error) {\n        throw new Error(\"خطأ في إنشاء أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n    return data;\n}\n// تحديث أداة ذكاء اصطناعي\nasync function updateAITool(id, toolData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').update({\n        ...toolData,\n        updated_at: new Date().toISOString()\n    }).eq('id', id).select().single();\n    if (error) {\n        throw new Error(\"خطأ في تحديث أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n    return data;\n}\n// حذف أداة ذكاء اصطناعي\nasync function deleteAITool(id) {\n    const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').delete().eq('id', id);\n    if (error) {\n        throw new Error(\"خطأ في حذف أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n}\n// ===== مساعدات الخدمات =====\n// جلب جميع الخدمات\nasync function getServices() {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').select('*').order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        throw new Error(\"خطأ في جلب الخدمات: \".concat(error.message));\n    }\n    return data;\n}\n// جلب خدمة واحدة بالـ ID\nasync function getServiceById(id) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').select('*').eq('id', id).single();\n    if (error) {\n        throw new Error(\"خطأ في جلب الخدمة: \".concat(error.message));\n    }\n    return data;\n}\n// إنشاء خدمة جديدة\nasync function createService(serviceData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').insert([\n        serviceData\n    ]).select().single();\n    if (error) {\n        throw new Error(\"خطأ في إنشاء الخدمة: \".concat(error.message));\n    }\n    return data;\n}\n// تحديث خدمة\nasync function updateService(id, serviceData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').update({\n        ...serviceData,\n        updated_at: new Date().toISOString()\n    }).eq('id', id).select().single();\n    if (error) {\n        throw new Error(\"خطأ في تحديث الخدمة: \".concat(error.message));\n    }\n    return data;\n}\n// حذف خدمة\nasync function deleteService(id) {\n    const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').delete().eq('id', id);\n    if (error) {\n        throw new Error(\"خطأ في حذف الخدمة: \".concat(error.message));\n    }\n}\n// ===== مساعدات الإحصائيات =====\n// جلب إحصائيات سريعة للوحة التحكم\nasync function getDashboardStats() {\n    try {\n        var _articlesResult_data, _articlesResult_data1;\n        const [articlesResult, aiToolsResult, servicesResult] = await Promise.all([\n            _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').select('status', {\n                count: 'exact'\n            }),\n            _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').select('status', {\n                count: 'exact'\n            }),\n            _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').select('status', {\n                count: 'exact'\n            })\n        ]);\n        const articlesCount = articlesResult.count || 0;\n        const publishedArticles = ((_articlesResult_data = articlesResult.data) === null || _articlesResult_data === void 0 ? void 0 : _articlesResult_data.filter((a)=>a.status === 'published').length) || 0;\n        const draftArticles = ((_articlesResult_data1 = articlesResult.data) === null || _articlesResult_data1 === void 0 ? void 0 : _articlesResult_data1.filter((a)=>a.status === 'draft').length) || 0;\n        const aiToolsCount = aiToolsResult.count || 0;\n        const servicesCount = servicesResult.count || 0;\n        return {\n            articles: {\n                total: articlesCount,\n                published: publishedArticles,\n                drafts: draftArticles\n            },\n            aiTools: aiToolsCount,\n            services: servicesCount\n        };\n    } catch (error) {\n        throw new Error(\"خطأ في جلب الإحصائيات: \".concat(error.message));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

});