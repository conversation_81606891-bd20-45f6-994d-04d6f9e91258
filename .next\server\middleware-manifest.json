{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?(?:\\/((?!api|_next\\/static|_next\\/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*))(\\.json)?[\\/#\\?]?$", "originalSource": "/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:svg|png|jpg|jpeg|gif|webp)$).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "OLVSvh6_Cu1ab3ilH8nNp", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "/YfLHL93idJtNcDMVrnlH0QqCddUJCc0sg891n9hXik=", "__NEXT_PREVIEW_MODE_ID": "e994f5f3971e84fc5ac2b3faa16229e7", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "ee6115eb7909c3dafc1a66ad37b415b69d9dd04019f5b987267d47742025090f", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "bceb2b12d6041371ad07bb6b4b513bb10edad44c4a430004ab246ed53183cbe8"}}}, "functions": {}, "sortedMiddleware": ["/"]}