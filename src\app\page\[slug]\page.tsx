// صفحة ديناميكية لعرض الصفحات الثابتة
import { notFound } from 'next/navigation';
import Link from 'next/link';
import { supabase, fixObjectEncoding } from '@/lib/supabase';
import { getStaticPageByKey } from '@/lib/static-data';
import { Metadata } from 'next';

interface PageData {
  id: string;
  page_key: string;
  title_ar: string;
  content_ar: string;
  title_en?: string;
  content_en?: string;
  meta_description?: string;
  meta_description_en?: string;
  meta_keywords?: string;
  meta_keywords_en?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

interface PageProps {
  params: Promise<{
    slug: string;
  }>;
}

// جلب بيانات الصفحة - مع fallback للبيانات الثابتة
async function getPageData(slug: string): Promise<PageData | null> {
  try {
    // محاولة جلب البيانات من Supabase أولاً
    const { data, error } = await supabase
      .from('site_pages')
      .select('*')
      .eq('page_key', slug)
      .eq('is_active', true)
      .single();

    if (!error && data) {
      // إصلاح encoding النص العربي
      return fixObjectEncoding(data) as PageData;
    }

    // في حالة فشل Supabase، استخدم البيانات الثابتة
    console.log('⚠️ Supabase failed, using static data for page:', slug);
    const staticPage = getStaticPageByKey(slug);

    if (staticPage) {
      return staticPage as PageData;
    }

    console.error('❌ Page not found in static data:', slug);
    return null;
  } catch (error) {
    console.error('❌ Error fetching page data, trying static fallback:', error);

    // fallback للبيانات الثابتة في حالة الخطأ
    const staticPage = getStaticPageByKey(slug);
    return staticPage ? staticPage as PageData : null;
  }
}

// إنشاء metadata ديناميكي
export async function generateMetadata({ params }: PageProps): Promise<Metadata> {
  const { slug } = await params;
  const pageData = await getPageData(slug);

  if (!pageData) {
    return {
      title: 'صفحة غير موجودة - TechnoFlash',
      description: 'الصفحة المطلوبة غير موجودة'
    };
  }

  // الصفحات التي يجب أن تكون بالإنجليزية للإعلانات
  const englishPages = ['about-us', 'contact-us', 'privacy-policy', 'terms-of-use'];
  const isEnglishPage = englishPages.includes(slug);

  const title = isEnglishPage ? (pageData.title_en || pageData.title_ar) : pageData.title_ar;
  const description = isEnglishPage ?
    (pageData.meta_description_en || pageData.meta_description || title) :
    (pageData.meta_description || title);
  const keywords = isEnglishPage ?
    (pageData.meta_keywords_en || pageData.meta_keywords || '') :
    (pageData.meta_keywords || '');
  const locale = isEnglishPage ? 'en_US' : 'ar_SA';

  return {
    title: `${title} - TechnoFlash`,
    description: description,
    keywords: keywords,
    openGraph: {
      title: `${title} - TechnoFlash`,
      description: description,
      type: 'website',
      locale: locale,
    },
    twitter: {
      card: 'summary_large_image',
      title: `${title} - TechnoFlash`,
      description: description,
    }
  };
}

// إنشاء الصفحات الثابتة في وقت البناء - مع fallback للبيانات الثابتة
export async function generateStaticParams() {
  try {
    // محاولة جلب البيانات من Supabase أولاً
    const { data, error } = await supabase
      .from('site_pages')
      .select('page_key')
      .eq('is_active', true);

    if (!error && data && data.length > 0) {
      console.log(`✅ Generated ${data.length} static params from Supabase`);
      return data.map((page) => ({
        slug: page.page_key,
      }));
    }

    // في حالة فشل Supabase، استخدم البيانات الثابتة
    console.log('⚠️ Supabase failed, using static data for generateStaticParams');
    const { getStaticPages } = await import('@/lib/static-data');
    const staticPages = getStaticPages();

    console.log(`✅ Generated ${staticPages.length} static params from static data`);
    return staticPages.map((page) => ({
      slug: page.page_key,
    }));
  } catch (error) {
    console.error('❌ Error in generateStaticParams, using static fallback:', error);

    // fallback للبيانات الثابتة
    try {
      const { getStaticPages } = await import('@/lib/static-data');
      const staticPages = getStaticPages();
      return staticPages.map((page) => ({
        slug: page.page_key,
      }));
    } catch (staticError) {
      console.error('❌ Static fallback also failed:', staticError);
      return [];
    }
  }
}

// مكون الصفحة الرئيسي
export default async function DynamicPage({ params }: PageProps) {
  const { slug } = await params;
  const pageData = await getPageData(slug);

  if (!pageData) {
    notFound();
  }

  // الصفحات التي يجب أن تكون بالإنجليزية للإعلانات
  const englishPages = ['about-us', 'contact-us', 'privacy-policy', 'terms-of-use'];
  const isEnglishPage = englishPages.includes(slug);

  return (
    <div className="min-h-screen py-20 px-4 bg-dark-background">
      <div className="container mx-auto max-w-4xl">
        {/* العنوان الرئيسي */}
        <header className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-bold text-white mb-4">
            {isEnglishPage ? (pageData.title_en || pageData.title_ar) : pageData.title_ar}
          </h1>
          {(isEnglishPage ? (pageData.meta_description_en || pageData.meta_description) : pageData.meta_description) && (
            <p className="text-xl text-dark-text-secondary max-w-2xl mx-auto">
              {isEnglishPage ? (pageData.meta_description_en || pageData.meta_description) : pageData.meta_description}
            </p>
          )}
        </header>

        {/* محتوى الصفحة */}
        <main className="bg-dark-card rounded-xl p-8 border border-gray-800">
          <div
            className="prose prose-lg prose-invert max-w-none"
            style={{
              color: '#e5e7eb',
              lineHeight: '1.8'
            }}
            dangerouslySetInnerHTML={{
              __html: isEnglishPage ? (pageData.content_en || pageData.content_ar) : pageData.content_ar
            }}
          />
        </main>

        {/* معلومات إضافية */}
        <footer className="mt-12 text-center">
          <div className="bg-dark-card rounded-lg p-6 border border-gray-800">
            <p className="text-dark-text-secondary text-sm mb-4">
              {isEnglishPage ? 'Last updated: ' : 'آخر تحديث: '}
              {new Date(pageData.updated_at).toLocaleDateString(isEnglishPage ? 'en-US' : 'ar-SA', {
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })}
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center items-center">
              <Link
                href="/"
                className="bg-primary hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300"
              >
                {isEnglishPage ? 'Back to Home' : 'العودة للرئيسية'}
              </Link>

              <Link
                href="/page/contact-us"
                className="border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300"
              >
                {isEnglishPage ? 'Contact Us' : 'تواصل معنا'}
              </Link>
            </div>
          </div>
        </footer>
      </div>
    </div>
  );
}

// تحسين الأداء - ISR محسن لتوفير الاستهلاك
export const revalidate = 604800; // أسبوع
export const dynamic = 'force-static';
