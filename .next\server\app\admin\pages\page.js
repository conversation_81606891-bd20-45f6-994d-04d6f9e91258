(()=>{var e={};e.id=785,e.ids=[785],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},17893:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>x,tree:()=>l});var s=r(65239),a=r(48088),i=r(88170),n=r.n(i),o=r(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);r.d(t,d);let l={children:["",{children:["admin",{children:["pages",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,60507)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\pages\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\4\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\4\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\4\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\4\\src\\app\\admin\\pages\\page.tsx"],u={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/pages/page",pathname:"/admin/pages",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,t,r)=>{"use strict";r.d(t,{ProtectedRoute:()=>n});var s=r(60687),a=r(63213),i=r(16189);function n({children:e}){let{user:t,loading:r}=(0,a.A)();return((0,i.useRouter)(),r)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"جاري التحقق من صلاحيات الوصول..."})]})}):t?(0,s.jsx)(s.Fragment,{children:e}):null}r(43210)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33134:(e,t,r)=>{Promise.resolve().then(r.bind(r,67083))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},60507:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\admin\\\\pages\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\app\\admin\\pages\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,t,r)=>{"use strict";r.d(t,{ProtectedRoute:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},71249:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(60687),a=r(43210),i=r(85814),n=r.n(i);function o(){let[e,t]=(0,a.useState)([]),[r,i]=(0,a.useState)(!0),[o,d]=(0,a.useState)(""),l=async()=>{try{i(!0);let e=await fetch("/api/pages?include_inactive=true"),r=await e.json();r.success?t(r.data):d(r.message||"فشل في جلب الصفحات")}catch(e){console.error("خطأ في جلب الصفحات:",e),d("حدث خطأ أثناء جلب الصفحات")}finally{i(!1)}},c=async(t,r)=>{try{let s=await fetch(`/api/pages/${t}`,{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!r,title_ar:e.find(e=>e.id===t)?.title_ar,content_ar:e.find(e=>e.id===t)?.content_ar})}),a=await s.json();a.success?await l():alert("فشل في تحديث حالة الصفحة: "+a.message)}catch(e){console.error("خطأ في تحديث الصفحة:",e),alert("حدث خطأ أثناء تحديث الصفحة")}},u=async(e,t)=>{if(confirm(`هل أنت متأكد من حذف صفحة "${t}"؟`))try{let t=await fetch(`/api/pages/${e}`,{method:"DELETE"}),r=await t.json();r.success?await l():alert("فشل في حذف الصفحة: "+r.message)}catch(e){console.error("خطأ في حذف الصفحة:",e),alert("حدث خطأ أثناء حذف الصفحة")}};return r?(0,s.jsx)("div",{className:"min-h-screen bg-dark-background p-6",children:(0,s.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})})}):(0,s.jsx)("div",{className:"min-h-screen bg-dark-background p-6",children:(0,s.jsxs)("div",{className:"container mx-auto",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"إدارة الصفحات"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"إدارة محتوى الصفحات الثابتة في الموقع"})]}),(0,s.jsxs)("div",{className:"flex gap-4",children:[(0,s.jsx)(n(),{href:"/admin/pages/new",className:"bg-primary hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"إضافة صفحة جديدة"}),(0,s.jsx)(n(),{href:"/admin",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"العودة للوحة التحكم"})]})]}),o&&(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-400",children:o})}),(0,s.jsx)("div",{className:"bg-dark-card rounded-lg border border-gray-800 overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"w-full",children:[(0,s.jsx)("thead",{className:"bg-dark-background",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"text-right p-4 text-white font-medium",children:"العنوان"}),(0,s.jsx)("th",{className:"text-right p-4 text-white font-medium",children:"مفتاح الصفحة"}),(0,s.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"الحالة"}),(0,s.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"الترتيب"}),(0,s.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"آخر تحديث"}),(0,s.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{children:e.map(e=>(0,s.jsxs)("tr",{className:"border-t border-gray-800 hover:bg-dark-background/50",children:[(0,s.jsx)("td",{className:"p-4",children:(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-white font-medium",children:e.title_ar}),e.meta_description&&(0,s.jsx)("p",{className:"text-dark-text-secondary text-sm mt-1 line-clamp-2",children:e.meta_description})]})}),(0,s.jsx)("td",{className:"p-4",children:(0,s.jsx)("code",{className:"bg-dark-background px-2 py-1 rounded text-primary text-sm",children:e.page_key})}),(0,s.jsx)("td",{className:"p-4 text-center",children:(0,s.jsx)("button",{onClick:()=>c(e.id,e.is_active),className:`px-3 py-1 rounded-full text-sm font-medium transition-colors duration-300 ${e.is_active?"bg-green-500/20 text-green-400 hover:bg-green-500/30":"bg-red-500/20 text-red-400 hover:bg-red-500/30"}`,children:e.is_active?"نشط":"غير نشط"})}),(0,s.jsx)("td",{className:"p-4 text-center",children:(0,s.jsx)("span",{className:"text-dark-text-secondary",children:e.display_order})}),(0,s.jsx)("td",{className:"p-4 text-center",children:(0,s.jsx)("span",{className:"text-dark-text-secondary text-sm",children:new Date(e.updated_at).toLocaleDateString("ar-SA")})}),(0,s.jsx)("td",{className:"p-4",children:(0,s.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,s.jsx)(n(),{href:`/page/${e.page_key}`,target:"_blank",className:"bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 p-2 rounded transition-colors duration-300",title:"عرض الصفحة",children:(0,s.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}),(0,s.jsx)(n(),{href:`/admin/pages/edit/${e.id}`,className:"bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 p-2 rounded transition-colors duration-300",title:"تعديل الصفحة",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,s.jsx)("button",{onClick:()=>u(e.id,e.title_ar),className:"bg-red-500/20 hover:bg-red-500/30 text-red-400 p-2 rounded transition-colors duration-300",title:"حذف الصفحة",children:(0,s.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]},e.id))})]})})}),0===e.length&&!r&&(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"w-16 h-16 bg-dark-card rounded-full flex items-center justify-center mx-auto mb-4",children:(0,s.jsx)("svg",{className:"w-8 h-8 text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsx)("h3",{className:"text-xl font-medium text-white mb-2",children:"لا توجد صفحات"}),(0,s.jsx)("p",{className:"text-dark-text-secondary mb-6",children:"لم يتم إنشاء أي صفحات بعد"}),(0,s.jsx)(n(),{href:"/admin/pages/new",className:"bg-primary hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300 inline-block",children:"إنشاء أول صفحة"})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80995:(e,t,r)=>{Promise.resolve().then(r.bind(r,60507))},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95067:(e,t,r)=>{Promise.resolve().then(r.bind(r,71249))},96182:(e,t,r)=>{Promise.resolve().then(r.bind(r,20769))},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>i});var s=r(37413),a=r(67083);function i({children:e}){return(0,s.jsx)(a.ProtectedRoute,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2714,6656],()=>r(17893));module.exports=s})();