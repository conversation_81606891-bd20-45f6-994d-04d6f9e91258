"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/ai-tools/page",{

/***/ "(app-pages-browser)/./src/components/ai-tools/LazyAIToolsGrid.tsx":
/*!*****************************************************!*\
  !*** ./src/components/ai-tools/LazyAIToolsGrid.tsx ***!
  \*****************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ LazyAIToolsGrid)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var next_image__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/image */ \"(app-pages-browser)/./node_modules/next/dist/api/image.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_4__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n/**\r\n * مكون تحميل تدريجي لأدوات الذكاء الاصطناعي\r\n * يحمل البيانات على دفعات لتحسين الأداء\r\n */ function LazyAIToolsGrid(param) {\n    let { initialTools = [], pageSize = 12, category = '', searchQuery = '', sortBy = 'latest' } = param;\n    _s();\n    const [tools, setTools] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(initialTools);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [page, setPage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    // تحميل المزيد من الأدوات\n    const loadMoreTools = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"LazyAIToolsGrid.useCallback[loadMoreTools]\": async ()=>{\n            if (loading || !hasMore) return;\n            try {\n                setLoading(true);\n                setError(null);\n                let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_2__.supabase.from('ai_tools').select('id, name, description, category, pricing, logo_url, website_url, slug, rating, created_at').in('status', [\n                    'published',\n                    'active'\n                ]).range(page * pageSize, (page + 1) * pageSize - 1);\n                // تطبيق الترتيب\n                switch(sortBy){\n                    case 'name':\n                        query = query.order('name', {\n                            ascending: true\n                        });\n                        break;\n                    case 'rating':\n                        query = query.order('rating', {\n                            ascending: false\n                        });\n                        break;\n                    case 'popular':\n                        query = query.order('click_count', {\n                            ascending: false\n                        });\n                        break;\n                    case 'latest':\n                    default:\n                        query = query.order('created_at', {\n                            ascending: false\n                        });\n                        break;\n                }\n                // فلترة حسب الفئة\n                if (category && category !== 'all') {\n                    query = query.eq('category', category);\n                }\n                // فلترة حسب البحث\n                if (searchQuery) {\n                    query = query.or(\"name.ilike.%\".concat(searchQuery, \"%,description.ilike.%\").concat(searchQuery, \"%\"));\n                }\n                const { data, error } = await query;\n                if (error) {\n                    console.error('Error loading tools:', error);\n                    setError('خطأ في تحميل الأدوات');\n                    return;\n                }\n                if (data && data.length > 0) {\n                    setTools({\n                        \"LazyAIToolsGrid.useCallback[loadMoreTools]\": (prev)=>page === 0 ? data : [\n                                ...prev,\n                                ...data\n                            ]\n                    }[\"LazyAIToolsGrid.useCallback[loadMoreTools]\"]);\n                    setPage({\n                        \"LazyAIToolsGrid.useCallback[loadMoreTools]\": (prev)=>prev + 1\n                    }[\"LazyAIToolsGrid.useCallback[loadMoreTools]\"]);\n                    // إذا كان عدد النتائج أقل من pageSize، فلا توجد صفحات أخرى\n                    if (data.length < pageSize) {\n                        setHasMore(false);\n                    }\n                } else {\n                    setHasMore(false);\n                }\n            } catch (error) {\n                console.error('Error in loadMoreTools:', error);\n                setError('خطأ في تحميل الأدوات');\n            } finally{\n                setLoading(false);\n            }\n        }\n    }[\"LazyAIToolsGrid.useCallback[loadMoreTools]\"], [\n        page,\n        pageSize,\n        category,\n        searchQuery,\n        sortBy,\n        loading,\n        hasMore\n    ]);\n    // إعادة تعيين عند تغيير الفلاتر\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LazyAIToolsGrid.useEffect\": ()=>{\n            setTools([]);\n            setPage(0);\n            setHasMore(true);\n            setError(null);\n        }\n    }[\"LazyAIToolsGrid.useEffect\"], [\n        category,\n        searchQuery,\n        sortBy\n    ]);\n    // تحميل الصفحة الأولى عند تغيير الفلاتر\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"LazyAIToolsGrid.useEffect\": ()=>{\n            if (tools.length === 0 && hasMore) {\n                loadMoreTools();\n            }\n        }\n    }[\"LazyAIToolsGrid.useEffect\"], [\n        tools.length,\n        hasMore,\n        loadMoreTools\n    ]);\n    const getPricingColor = (pricing)=>{\n        switch(pricing){\n            case 'free':\n                return 'border-green-500 text-green-400 bg-green-500/10';\n            case 'freemium':\n                return 'border-yellow-500 text-yellow-400 bg-yellow-500/10';\n            case 'paid':\n                return 'border-red-500 text-red-400 bg-red-500/10';\n            default:\n                return 'border-gray-500 text-gray-400 bg-gray-500/10';\n        }\n    };\n    const getPricingText = (pricing)=>{\n        switch(pricing){\n            case 'free':\n                return 'مجاني';\n            case 'freemium':\n                return 'مجاني جزئياً';\n            case 'paid':\n                return 'مدفوع';\n            default:\n                return 'غير محدد';\n        }\n    };\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"text-center py-12\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-red-400 mb-4\",\n                    children: [\n                        \"❌ \",\n                        error\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                    lineNumber: 151,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: ()=>{\n                        setError(null);\n                        loadMoreTools();\n                    },\n                    className: \"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90 transition-colors\",\n                    children: \"إعادة المحاولة\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                    lineNumber: 152,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n            lineNumber: 150,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: tools.map((tool)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-dark-card rounded-xl overflow-hidden border border-gray-800 hover:border-primary/50 transition-all duration-300 group\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"relative h-48 bg-gradient-to-br from-primary/10 to-blue-600/10\",\n                                children: tool.logo_url ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_image__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                    src: tool.logo_url,\n                                    alt: tool.name,\n                                    fill: true,\n                                    style: {\n                                        objectFit: \"contain\"\n                                    },\n                                    className: \"p-4 group-hover:scale-105 transition-transform duration-300\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                    lineNumber: 177,\n                                    columnNumber: 17\n                                }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-full h-full flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-16 h-16 bg-primary/20 rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-2xl font-bold text-primary\",\n                                            children: tool.name.charAt(0)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                            lineNumber: 187,\n                                            columnNumber: 21\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 186,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                    lineNumber: 185,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 175,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-start justify-between mb-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                className: \"text-xl font-bold text-white group-hover:text-primary transition-colors duration-300 line-clamp-1\",\n                                                children: tool.name\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 198,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"px-2 py-1 rounded-full text-xs font-medium border \".concat(getPricingColor(tool.pricing)),\n                                                children: getPricingText(tool.pricing)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 201,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 197,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-dark-text-secondary mb-4 line-clamp-2\",\n                                        children: tool.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 206,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex items-center justify-between mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                className: \"bg-primary/20 text-primary px-2 py-1 rounded text-sm\",\n                                                children: tool.category\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 212,\n                                                columnNumber: 17\n                                            }, this),\n                                            tool.rating && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-yellow-400 mr-1\",\n                                                        children: \"⭐\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                        lineNumber: 217,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-sm text-gray-300\",\n                                                        children: tool.rating\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                        lineNumber: 218,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 216,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 211,\n                                        columnNumber: 15\n                                    }, this),\n                                    tool.features && tool.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"mb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex flex-wrap gap-1\",\n                                            children: [\n                                                tool.features.slice(0, 3).map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"bg-gray-800 text-gray-300 px-2 py-1 rounded text-xs\",\n                                                        children: feature\n                                                    }, index, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                        lineNumber: 228,\n                                                        columnNumber: 23\n                                                    }, this)),\n                                                tool.features.length > 3 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-gray-500 text-xs\",\n                                                    children: [\n                                                        \"+\",\n                                                        tool.features.length - 3,\n                                                        \" المزيد\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                    lineNumber: 236,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                            lineNumber: 226,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 225,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex space-x-2 space-x-reverse\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_4___default()), {\n                                                href: \"/ai-tools/\".concat(tool.slug),\n                                                className: \"flex-1 bg-primary text-white text-center py-2 px-4 rounded-lg hover:bg-primary/90 transition-colors duration-300 text-sm font-medium\",\n                                                children: \"عرض التفاصيل\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            tool.website_url && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                                href: tool.website_url,\n                                                target: \"_blank\",\n                                                rel: \"noopener noreferrer\",\n                                                className: \"bg-gray-700 text-white py-2 px-3 rounded-lg hover:bg-gray-600 transition-colors duration-300 text-sm\",\n                                                children: \"\\uD83D\\uDD17\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                                lineNumber: 253,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 245,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 196,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, tool.id, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 170,\n                        columnNumber: 11\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 168,\n                columnNumber: 7\n            }, this),\n            hasMore && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                    onClick: loadMoreTools,\n                    disabled: loading,\n                    className: \"bg-primary text-white px-8 py-3 rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-300 font-medium\",\n                    children: loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 278,\n                                columnNumber: 17\n                            }, this),\n                            \"جاري التحميل...\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 277,\n                        columnNumber: 15\n                    }, this) : 'تحميل المزيد'\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                    lineNumber: 271,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 270,\n                columnNumber: 9\n            }, this),\n            !loading && tools.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center py-12\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-gray-400 mb-4\",\n                        children: \"\\uD83D\\uDD0D لا توجد أدوات متاحة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 291,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-500\",\n                        children: \"جرب تغيير معايير البحث أو الفلترة\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 290,\n                columnNumber: 9\n            }, this),\n            loading && tools.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6\",\n                children: Array.from({\n                    length: pageSize\n                }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-dark-card rounded-xl overflow-hidden border border-gray-800 animate-pulse\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-48 bg-gray-700\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 304,\n                                columnNumber: 15\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"p-6\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-4 bg-gray-700 rounded mb-3\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 306,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-700 rounded mb-2\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-3 bg-gray-700 rounded mb-4 w-3/4\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"h-8 bg-gray-700 rounded\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                        lineNumber: 309,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                                lineNumber: 305,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, index, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                        lineNumber: 300,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n                lineNumber: 298,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ai-tools\\\\LazyAIToolsGrid.tsx\",\n        lineNumber: 166,\n        columnNumber: 5\n    }, this);\n}\n_s(LazyAIToolsGrid, \"iY0f6Plr+RrkJ/DhNnU8aITaklE=\");\n_c = LazyAIToolsGrid;\nvar _c;\n$RefreshReg$(_c, \"LazyAIToolsGrid\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ai-tools/LazyAIToolsGrid.tsx\n"));

/***/ })

});