'use client';

import { useState, useCallback } from 'react';
import LazyAIToolsGrid from './LazyAIToolsGrid';

interface AITool {
  id: string;
  name: string;
  description: string;
  category: string;
  pricing: string;
  logo_url?: string;
  website_url?: string;
  slug: string;
  rating?: string;
  features?: string[];
  created_at: string;
}

interface AIToolsSearchWrapperProps {
  initialTools: AITool[];
  categories: string[];
  pageSize?: number;
}

/**
 * مكون يدير البحث والفلترة لأدوات الذكاء الاصطناعي
 */
export default function AIToolsSearchWrapper({ 
  initialTools, 
  categories, 
  pageSize = 12 
}: AIToolsSearchWrapperProps) {
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [sortBy, setSortBy] = useState('latest');

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  }, []);

  const handleCategoryChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedCategory(e.target.value);
  }, []);

  const handleSortChange = useCallback((e: React.ChangeEvent<HTMLSelectElement>) => {
    setSortBy(e.target.value);
  }, []);

  const handleReset = useCallback(() => {
    setSearchQuery('');
    setSelectedCategory('all');
    setSortBy('latest');
  }, []);

  return (
    <div>
      {/* شريط البحث والفلاتر */}
      <div className="bg-dark-card rounded-xl p-6 mb-8 border border-gray-800">
        <div className="flex flex-col lg:flex-row gap-4">
          {/* شريط البحث */}
          <div className="flex-1">
            <div className="relative">
              <input
                type="text"
                placeholder="البحث في الأدوات..."
                value={searchQuery}
                onChange={handleSearchChange}
                className="w-full bg-dark-background border border-gray-700 text-white px-4 py-3 pr-12 rounded-lg focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300"
                aria-label="البحث في أدوات الذكاء الاصطناعي"
              />
              <div className="absolute left-3 top-1/2 transform -translate-y-1/2">
                <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* الفلاتر */}
          <div className="flex flex-col sm:flex-row gap-4">
            {/* فلتر الفئة */}
            <select
              value={selectedCategory}
              onChange={handleCategoryChange}
              className="bg-dark-background border border-gray-700 text-white px-4 py-3 rounded-lg focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300 min-w-[150px]"
              aria-label="فلترة حسب الفئة"
            >
              <option value="all">جميع الفئات</option>
              {categories.map((category) => (
                <option key={category} value={category}>
                  {category}
                </option>
              ))}
            </select>

            {/* فلتر الترتيب */}
            <select
              value={sortBy}
              onChange={handleSortChange}
              className="bg-dark-background border border-gray-700 text-white px-4 py-3 rounded-lg focus:outline-none focus:border-primary focus:ring-2 focus:ring-primary/20 transition-all duration-300 min-w-[150px]"
              aria-label="ترتيب النتائج"
            >
              <option value="latest">الأحدث</option>
              <option value="name">الاسم</option>
              <option value="rating">التقييم</option>
              <option value="popular">الأكثر شعبية</option>
            </select>

            {/* زر إعادة التعيين */}
            {(searchQuery || selectedCategory !== 'all' || sortBy !== 'latest') && (
              <button
                onClick={handleReset}
                className="bg-gray-600 hover:bg-gray-500 text-white px-4 py-3 rounded-lg transition-colors duration-300 whitespace-nowrap"
                aria-label="إعادة تعيين الفلاتر"
              >
                إعادة تعيين
              </button>
            )}
          </div>
        </div>

        {/* مؤشرات البحث */}
        {searchQuery && (
          <div className="mt-4 text-sm text-gray-400">
            البحث عن: <span className="text-primary font-medium">"{searchQuery}"</span>
          </div>
        )}
      </div>

      {/* شبكة الأدوات مع الفلاتر المطبقة */}
      <LazyAIToolsGrid
        initialTools={initialTools}
        pageSize={pageSize}
        category={selectedCategory}
        searchQuery={searchQuery}
        sortBy={sortBy}
      />
    </div>
  );
}
