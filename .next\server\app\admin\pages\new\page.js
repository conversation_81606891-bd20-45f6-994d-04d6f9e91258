(()=>{var e={};e.id=5640,e.ids=[5640],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10659:(e,r,t)=>{Promise.resolve().then(t.bind(t,20156))},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20156:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>d});var s=t(60687),a=t(43210),i=t(16189),n=t(85814),o=t.n(n);function d(){let e=(0,i.useRouter)(),[r,t]=(0,a.useState)(!1),[n,d]=(0,a.useState)(""),[l,c]=(0,a.useState)({page_key:"",title_ar:"",content_ar:"",meta_description:"",meta_keywords:"",display_order:0}),u=e=>{let{name:r,value:t}=e.target;if(c(e=>({...e,[r]:t})),"title_ar"===r){let e=t.toLowerCase().replace(/[^\w\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").trim();c(r=>({...r,page_key:e}))}},m=async r=>{if(r.preventDefault(),!l.title_ar.trim()||!l.content_ar.trim()||!l.page_key.trim())return void d("العنوان ومفتاح الصفحة والمحتوى مطلوبة");try{t(!0),d("");let r=await fetch("/api/pages",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(l)}),s=await r.json();s.success?e.push("/admin/pages"):d(s.message||"فشل في إنشاء الصفحة")}catch(e){console.error("خطأ في إنشاء الصفحة:",e),d("حدث خطأ أثناء إنشاء الصفحة")}finally{t(!1)}};return(0,s.jsx)("div",{className:"min-h-screen bg-dark-background p-6",children:(0,s.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"إضافة صفحة جديدة"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"إنشاء صفحة جديدة في الموقع"})]}),(0,s.jsx)(o(),{href:"/admin/pages",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"العودة للقائمة"})]}),n&&(0,s.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:(0,s.jsx)("p",{className:"text-red-400",children:n})}),(0,s.jsxs)("form",{onSubmit:m,className:"space-y-6",children:[(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-6",children:"المعلومات الأساسية"}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"عنوان الصفحة *"}),(0,s.jsx)("input",{type:"text",name:"title_ar",value:l.title_ar,onChange:u,required:!0,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary",placeholder:"مثال: من نحن"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"مفتاح الصفحة *"}),(0,s.jsx)("input",{type:"text",name:"page_key",value:l.page_key,onChange:u,required:!0,pattern:"[a-z0-9-]+",className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary",placeholder:"مثال: about-us"}),(0,s.jsx)("p",{className:"text-xs text-dark-text-secondary mt-1",children:"يتم إنشاؤه تلقائياً من العنوان، يمكنك تعديله"})]}),(0,s.jsxs)("div",{className:"md:col-span-2",children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"ترتيب العرض"}),(0,s.jsx)("input",{type:"number",name:"display_order",value:l.display_order,onChange:u,min:"0",className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary"})]})]})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-6",children:"تحسين محركات البحث (SEO)"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"وصف الصفحة (Meta Description)"}),(0,s.jsx)("textarea",{name:"meta_description",value:l.meta_description,onChange:u,rows:3,maxLength:160,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary",placeholder:"وصف مختصر للصفحة (160 حرف كحد أقصى)"}),(0,s.jsxs)("p",{className:"text-xs text-dark-text-secondary mt-1",children:[l.meta_description.length,"/160 حرف"]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-sm font-medium text-white mb-2",children:"الكلمات المفتاحية (Meta Keywords)"}),(0,s.jsx)("input",{type:"text",name:"meta_keywords",value:l.meta_keywords,onChange:u,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary",placeholder:"كلمة1, كلمة2, كلمة3"})]})]})]}),(0,s.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,s.jsx)("h2",{className:"text-xl font-semibold text-white mb-6",children:"محتوى الصفحة *"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("textarea",{name:"content_ar",value:l.content_ar,onChange:u,rows:20,required:!0,className:"w-full px-3 py-2 bg-dark-background border border-gray-600 rounded-md text-white focus:outline-none focus:ring-2 focus:ring-primary font-mono text-sm",placeholder:"أدخل محتوى الصفحة بصيغة HTML..."}),(0,s.jsxs)("div",{className:"text-xs text-dark-text-secondary",children:[(0,s.jsx)("p",{className:"mb-2",children:"يمكنك استخدام HTML لتنسيق المحتوى. أمثلة:"}),(0,s.jsxs)("ul",{className:"list-disc list-inside space-y-1 text-gray-400",children:[(0,s.jsx)("li",{children:"<h2>عنوان فرعي</h2>"}),(0,s.jsx)("li",{children:"<p>فقرة نصية</p>"}),(0,s.jsx)("li",{children:"<strong>نص عريض</strong>"}),(0,s.jsx)("li",{children:'<a href="رابط">نص الرابط</a>'}),(0,s.jsx)("li",{children:"<ul><li>عنصر قائمة</li></ul>"})]})]})]})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)(o(),{href:"/admin/pages",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"إلغاء"}),(0,s.jsx)("button",{type:"submit",disabled:r,className:"bg-primary hover:bg-blue-600 disabled:bg-gray-600 text-white px-8 py-3 rounded-lg font-medium transition-colors duration-300 disabled:cursor-not-allowed",children:r?"جاري الإنشاء...":"إنشاء الصفحة"})]})]})]})})}},20769:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>n});var s=t(60687),a=t(63213),i=t(16189);function n({children:e}){let{user:r,loading:t}=(0,a.A)();return((0,i.useRouter)(),t)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"جاري التحقق من صلاحيات الوصول..."})]})}):r?(0,s.jsx)(s.Fragment,{children:e}):null}t(43210)},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33134:(e,r,t)=>{Promise.resolve().then(t.bind(t,67083))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},34635:(e,r,t)=>{Promise.resolve().then(t.bind(t,61834))},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},61834:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\admin\\\\pages\\\\new\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\app\\admin\\pages\\new\\page.tsx","default")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},74941:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>n.a,__next_app__:()=>u,pages:()=>c,routeModule:()=>m,tree:()=>l});var s=t(65239),a=t(48088),i=t(88170),n=t.n(i),o=t(30893),d={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>o[e]);t.d(r,d);let l={children:["",{children:["admin",{children:["pages",{children:["new",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,61834)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\pages\\new\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(t.bind(t,99111)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\4\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\4\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\4\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\4\\src\\app\\admin\\pages\\new\\page.tsx"],u={require:t,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/admin/pages/new/page",pathname:"/admin/pages/new",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96182:(e,r,t)=>{Promise.resolve().then(t.bind(t,20769))},99111:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413),a=t(67083);function i({children:e}){return(0,s.jsx)(a.ProtectedRoute,{children:e})}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2714,6656],()=>t(74941));module.exports=s})();