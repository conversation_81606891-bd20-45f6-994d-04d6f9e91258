/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/pages/route";
exports.ids = ["app/api/pages/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpages%2Froute&page=%2Fapi%2Fpages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpages%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpages%2Froute&page=%2Fapi%2Fpages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpages%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ismail_4_src_app_api_pages_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/pages/route.ts */ \"(rsc)/./src/app/api/pages/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/pages/route\",\n        pathname: \"/api/pages\",\n        filename: \"route\",\n        bundlePath: \"app/api/pages/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\api\\\\pages\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ismail_4_src_app_api_pages_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpages%2Froute&page=%2Fapi%2Fpages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpages%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/pages/route.ts":
/*!************************************!*\
  !*** ./src/app/api/pages/route.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/supabase */ \"(rsc)/./src/lib/supabase.ts\");\n/* harmony import */ var _lib_auth_middleware__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/auth-middleware */ \"(rsc)/./src/lib/auth-middleware.ts\");\n/* harmony import */ var _lib_sanitize__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/sanitize */ \"(rsc)/./src/lib/sanitize.ts\");\n/* harmony import */ var _lib_rate_limit__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/lib/rate-limit */ \"(rsc)/./src/lib/rate-limit.ts\");\n// API لإدارة الصفحات الثابتة\n\n\n\n\n\n// جلب جميع الصفحات\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const includeInactive = searchParams.get('include_inactive') === 'true';\n        let query = _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('site_pages').select('*').order('display_order', {\n            ascending: true\n        });\n        // إذا لم يطلب المستخدم الصفحات غير النشطة، فلتر فقط النشطة\n        if (!includeInactive) {\n            query = query.eq('is_active', true);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('خطأ في جلب الصفحات:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'فشل في جلب الصفحات'\n            }, {\n                status: 500\n            });\n        }\n        // إصلاح encoding النص العربي\n        const fixedData = data?.map((page)=>(0,_lib_supabase__WEBPACK_IMPORTED_MODULE_1__.fixObjectEncoding)(page)) || [];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            data: fixedData\n        });\n    } catch (error) {\n        console.error('خطأ في API الصفحات:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'خطأ في الخادم'\n        }, {\n            status: 500\n        });\n    }\n}\n// إنشاء صفحة جديدة - محمي بالمصادقة\nconst POST = (0,_lib_auth_middleware__WEBPACK_IMPORTED_MODULE_2__.createAuthenticatedHandler)(async (request, context, user)=>{\n    try {\n        // Rate limiting\n        const clientIP = request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown';\n        const rateLimitResult = (0,_lib_rate_limit__WEBPACK_IMPORTED_MODULE_4__.rateLimit)(`create-page-${clientIP}`, 5, 300); // 5 طلبات كل 5 دقائق\n        if (!rateLimitResult.success) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'تم تجاوز الحد الأقصى للطلبات'\n            }, {\n                status: 429,\n                headers: (0,_lib_rate_limit__WEBPACK_IMPORTED_MODULE_4__.getRateLimitHeaders)(rateLimitResult)\n            });\n        }\n        const body = await request.json();\n        const { page_key, title_ar, content_ar, meta_description, meta_keywords, display_order } = body;\n        // التحقق من صحة البيانات\n        if (!(0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.validateInput)(page_key, 'text') || !(0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.validateInput)(title_ar, 'text') || !(0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.validateInput)(content_ar, 'html')) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'البيانات المدخلة غير صحيحة'\n            }, {\n                status: 400\n            });\n        }\n        // تنظيف البيانات\n        const cleanPageKey = (0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(page_key);\n        const cleanTitle = (0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(title_ar);\n        const cleanContent = (0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.sanitizeHtml)(content_ar);\n        const cleanMetaDescription = meta_description ? (0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(meta_description) : null;\n        const cleanMetaKeywords = meta_keywords ? (0,_lib_sanitize__WEBPACK_IMPORTED_MODULE_3__.sanitizeText)(meta_keywords) : null;\n        // التحقق من عدم وجود page_key مكرر\n        const { data: existingPage } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('site_pages').select('id').eq('page_key', cleanPageKey).single();\n        if (existingPage) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'مفتاح الصفحة موجود بالفعل'\n            }, {\n                status: 400\n            });\n        }\n        // إنشاء الصفحة الجديدة\n        const { data, error } = await _lib_supabase__WEBPACK_IMPORTED_MODULE_1__.supabase.from('site_pages').insert([\n            {\n                page_key: cleanPageKey,\n                title_ar: cleanTitle,\n                content_ar: cleanContent,\n                meta_description: cleanMetaDescription,\n                meta_keywords: cleanMetaKeywords,\n                display_order: display_order || 0,\n                is_active: true\n            }\n        ]).select().single();\n        if (error) {\n            console.error('خطأ في إنشاء الصفحة:', error);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                success: false,\n                message: 'فشل في إنشاء الصفحة'\n            }, {\n                status: 500\n            });\n        }\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: true,\n            message: 'تم إنشاء الصفحة بنجاح',\n            data\n        }, {\n            headers: (0,_lib_rate_limit__WEBPACK_IMPORTED_MODULE_4__.getRateLimitHeaders)(rateLimitResult)\n        });\n    } catch (error) {\n        console.error('خطأ في إنشاء الصفحة:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            message: 'خطأ في الخادم'\n        }, {\n            status: 500\n        });\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/pages/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/auth-middleware.ts":
/*!************************************!*\
  !*** ./src/lib/auth-middleware.ts ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAuthenticatedHandler: () => (/* binding */ createAuthenticatedHandler),\n/* harmony export */   verifyAuth: () => (/* binding */ verifyAuth)\n/* harmony export */ });\n/* harmony import */ var _supabase_ssr__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/ssr */ \"(rsc)/./node_modules/@supabase/ssr/dist/index.mjs\");\n// Middleware للتحقق من المصادقة في APIs\n\nasync function verifyAuth(request) {\n    try {\n        const supabase = (0,_supabase_ssr__WEBPACK_IMPORTED_MODULE_0__.createServerClient)(\"https://zgktrwpladrkhhemhnni.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04\", {\n            cookies: {\n                get (name) {\n                    return request.cookies.get(name)?.value;\n                },\n                set () {},\n                remove () {}\n            }\n        });\n        const { data: { session }, error } = await supabase.auth.getSession();\n        console.log('Auth verification:', {\n            hasSession: !!session,\n            error: error?.message,\n            userId: session?.user?.id\n        });\n        if (error || !session) {\n            // في بيئة التطوير أو إذا كان الموقع في production، نسمح بالوصول مؤقتاً\n            console.log('No valid session found, allowing access for development/testing');\n            return {\n                authenticated: true,\n                user: {\n                    id: 'dev-user',\n                    email: '<EMAIL>'\n                }\n            };\n        }\n        return {\n            authenticated: true,\n            user: session.user\n        };\n    } catch (error) {\n        console.error('Auth verification error:', error);\n        // في حالة خطأ، نسمح بالوصول مؤقتاً للتطوير والاختبار\n        console.log('Auth error, allowing access for development/testing');\n        return {\n            authenticated: true,\n            user: {\n                id: 'dev-user',\n                email: '<EMAIL>'\n            }\n        };\n    }\n}\nfunction createAuthenticatedHandler(handler) {\n    return async (request, context)=>{\n        const auth = await verifyAuth(request);\n        if (!auth.authenticated) {\n            return Response.json({\n                success: false,\n                message: 'غير مصرح بالوصول'\n            }, {\n                status: 401\n            });\n        }\n        return handler(request, context, auth.user);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/auth-middleware.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/rate-limit.ts":
/*!*******************************!*\
  !*** ./src/lib/rate-limit.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRateLimitHeaders: () => (/* binding */ getRateLimitHeaders),\n/* harmony export */   rateLimit: () => (/* binding */ rateLimit)\n/* harmony export */ });\n// نظام Rate Limiting بسيط\nconst store = {};\nfunction rateLimit(identifier, limit = 10, windowMs = 60000 // دقيقة واحدة\n) {\n    const now = Date.now();\n    const key = identifier;\n    // تنظيف البيانات القديمة\n    if (store[key] && now > store[key].resetTime) {\n        delete store[key];\n    }\n    // إنشاء أو تحديث العداد\n    if (!store[key]) {\n        store[key] = {\n            count: 1,\n            resetTime: now + windowMs\n        };\n        return {\n            success: true,\n            remaining: limit - 1,\n            resetTime: store[key].resetTime\n        };\n    }\n    // التحقق من الحد الأقصى\n    if (store[key].count >= limit) {\n        return {\n            success: false,\n            remaining: 0,\n            resetTime: store[key].resetTime\n        };\n    }\n    // زيادة العداد\n    store[key].count++;\n    return {\n        success: true,\n        remaining: limit - store[key].count,\n        resetTime: store[key].resetTime\n    };\n}\nfunction getRateLimitHeaders(result) {\n    return {\n        'X-RateLimit-Limit': '10',\n        'X-RateLimit-Remaining': result.remaining.toString(),\n        'X-RateLimit-Reset': new Date(result.resetTime).toISOString()\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3JhdGUtbGltaXQudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQSwwQkFBMEI7QUFRMUIsTUFBTUEsUUFBd0IsQ0FBQztBQUV4QixTQUFTQyxVQUNkQyxVQUFrQixFQUNsQkMsUUFBZ0IsRUFBRSxFQUNsQkMsV0FBbUIsTUFBTSxjQUFjO0FBQWY7SUFFeEIsTUFBTUMsTUFBTUMsS0FBS0QsR0FBRztJQUNwQixNQUFNRSxNQUFNTDtJQUVaLHlCQUF5QjtJQUN6QixJQUFJRixLQUFLLENBQUNPLElBQUksSUFBSUYsTUFBTUwsS0FBSyxDQUFDTyxJQUFJLENBQUNDLFNBQVMsRUFBRTtRQUM1QyxPQUFPUixLQUFLLENBQUNPLElBQUk7SUFDbkI7SUFFQSx3QkFBd0I7SUFDeEIsSUFBSSxDQUFDUCxLQUFLLENBQUNPLElBQUksRUFBRTtRQUNmUCxLQUFLLENBQUNPLElBQUksR0FBRztZQUNYRSxPQUFPO1lBQ1BELFdBQVdILE1BQU1EO1FBQ25CO1FBQ0EsT0FBTztZQUNMTSxTQUFTO1lBQ1RDLFdBQVdSLFFBQVE7WUFDbkJLLFdBQVdSLEtBQUssQ0FBQ08sSUFBSSxDQUFDQyxTQUFTO1FBQ2pDO0lBQ0Y7SUFFQSx3QkFBd0I7SUFDeEIsSUFBSVIsS0FBSyxDQUFDTyxJQUFJLENBQUNFLEtBQUssSUFBSU4sT0FBTztRQUM3QixPQUFPO1lBQ0xPLFNBQVM7WUFDVEMsV0FBVztZQUNYSCxXQUFXUixLQUFLLENBQUNPLElBQUksQ0FBQ0MsU0FBUztRQUNqQztJQUNGO0lBRUEsZUFBZTtJQUNmUixLQUFLLENBQUNPLElBQUksQ0FBQ0UsS0FBSztJQUVoQixPQUFPO1FBQ0xDLFNBQVM7UUFDVEMsV0FBV1IsUUFBUUgsS0FBSyxDQUFDTyxJQUFJLENBQUNFLEtBQUs7UUFDbkNELFdBQVdSLEtBQUssQ0FBQ08sSUFBSSxDQUFDQyxTQUFTO0lBQ2pDO0FBQ0Y7QUFFTyxTQUFTSSxvQkFBb0JDLE1BQW9DO0lBQ3RFLE9BQU87UUFDTCxxQkFBcUI7UUFDckIseUJBQXlCQSxPQUFPRixTQUFTLENBQUNHLFFBQVE7UUFDbEQscUJBQXFCLElBQUlSLEtBQUtPLE9BQU9MLFNBQVMsRUFBRU8sV0FBVztJQUM3RDtBQUNGIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXGlzbWFpbFxcNFxcc3JjXFxsaWJcXHJhdGUtbGltaXQudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g2YbYuNin2YUgUmF0ZSBMaW1pdGluZyDYqNiz2YrYt1xyXG5pbnRlcmZhY2UgUmF0ZUxpbWl0U3RvcmUge1xyXG4gIFtrZXk6IHN0cmluZ106IHtcclxuICAgIGNvdW50OiBudW1iZXI7XHJcbiAgICByZXNldFRpbWU6IG51bWJlcjtcclxuICB9O1xyXG59XHJcblxyXG5jb25zdCBzdG9yZTogUmF0ZUxpbWl0U3RvcmUgPSB7fTtcclxuXHJcbmV4cG9ydCBmdW5jdGlvbiByYXRlTGltaXQoXHJcbiAgaWRlbnRpZmllcjogc3RyaW5nLFxyXG4gIGxpbWl0OiBudW1iZXIgPSAxMCxcclxuICB3aW5kb3dNczogbnVtYmVyID0gNjAwMDAgLy8g2K/ZgtmK2YLYqSDZiNin2K3Yr9ipXHJcbik6IHsgc3VjY2VzczogYm9vbGVhbjsgcmVtYWluaW5nOiBudW1iZXI7IHJlc2V0VGltZTogbnVtYmVyIH0ge1xyXG4gIGNvbnN0IG5vdyA9IERhdGUubm93KCk7XHJcbiAgY29uc3Qga2V5ID0gaWRlbnRpZmllcjtcclxuXHJcbiAgLy8g2KrZhti42YrZgSDYp9mE2KjZitin2YbYp9iqINin2YTZgtiv2YrZhdipXHJcbiAgaWYgKHN0b3JlW2tleV0gJiYgbm93ID4gc3RvcmVba2V5XS5yZXNldFRpbWUpIHtcclxuICAgIGRlbGV0ZSBzdG9yZVtrZXldO1xyXG4gIH1cclxuXHJcbiAgLy8g2KXZhti02KfYoSDYo9mIINiq2K3Yr9mK2Ksg2KfZhNi52K/Yp9ivXHJcbiAgaWYgKCFzdG9yZVtrZXldKSB7XHJcbiAgICBzdG9yZVtrZXldID0ge1xyXG4gICAgICBjb3VudDogMSxcclxuICAgICAgcmVzZXRUaW1lOiBub3cgKyB3aW5kb3dNc1xyXG4gICAgfTtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHN1Y2Nlc3M6IHRydWUsXHJcbiAgICAgIHJlbWFpbmluZzogbGltaXQgLSAxLFxyXG4gICAgICByZXNldFRpbWU6IHN0b3JlW2tleV0ucmVzZXRUaW1lXHJcbiAgICB9O1xyXG4gIH1cclxuXHJcbiAgLy8g2KfZhNiq2K3ZgtmCINmF2YYg2KfZhNit2K8g2KfZhNij2YLYtdmJXHJcbiAgaWYgKHN0b3JlW2tleV0uY291bnQgPj0gbGltaXQpIHtcclxuICAgIHJldHVybiB7XHJcbiAgICAgIHN1Y2Nlc3M6IGZhbHNlLFxyXG4gICAgICByZW1haW5pbmc6IDAsXHJcbiAgICAgIHJlc2V0VGltZTogc3RvcmVba2V5XS5yZXNldFRpbWVcclxuICAgIH07XHJcbiAgfVxyXG5cclxuICAvLyDYstmK2KfYr9ipINin2YTYudiv2KfYr1xyXG4gIHN0b3JlW2tleV0uY291bnQrKztcclxuICBcclxuICByZXR1cm4ge1xyXG4gICAgc3VjY2VzczogdHJ1ZSxcclxuICAgIHJlbWFpbmluZzogbGltaXQgLSBzdG9yZVtrZXldLmNvdW50LFxyXG4gICAgcmVzZXRUaW1lOiBzdG9yZVtrZXldLnJlc2V0VGltZVxyXG4gIH07XHJcbn1cclxuXHJcbmV4cG9ydCBmdW5jdGlvbiBnZXRSYXRlTGltaXRIZWFkZXJzKHJlc3VsdDogUmV0dXJuVHlwZTx0eXBlb2YgcmF0ZUxpbWl0Pikge1xyXG4gIHJldHVybiB7XHJcbiAgICAnWC1SYXRlTGltaXQtTGltaXQnOiAnMTAnLFxyXG4gICAgJ1gtUmF0ZUxpbWl0LVJlbWFpbmluZyc6IHJlc3VsdC5yZW1haW5pbmcudG9TdHJpbmcoKSxcclxuICAgICdYLVJhdGVMaW1pdC1SZXNldCc6IG5ldyBEYXRlKHJlc3VsdC5yZXNldFRpbWUpLnRvSVNPU3RyaW5nKClcclxuICB9O1xyXG59XHJcbiJdLCJuYW1lcyI6WyJzdG9yZSIsInJhdGVMaW1pdCIsImlkZW50aWZpZXIiLCJsaW1pdCIsIndpbmRvd01zIiwibm93IiwiRGF0ZSIsImtleSIsInJlc2V0VGltZSIsImNvdW50Iiwic3VjY2VzcyIsInJlbWFpbmluZyIsImdldFJhdGVMaW1pdEhlYWRlcnMiLCJyZXN1bHQiLCJ0b1N0cmluZyIsInRvSVNPU3RyaW5nIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/rate-limit.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/sanitize.ts":
/*!*****************************!*\
  !*** ./src/lib/sanitize.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   sanitizeHtml: () => (/* binding */ sanitizeHtml),\n/* harmony export */   sanitizeText: () => (/* binding */ sanitizeText),\n/* harmony export */   validateInput: () => (/* binding */ validateInput)\n/* harmony export */ });\n// مكتبة تنظيف وتعقيم البيانات\nfunction sanitizeHtml(html) {\n    // قائمة العناصر المسموحة\n    const allowedTags = [\n        'p',\n        'br',\n        'strong',\n        'em',\n        'u',\n        'h1',\n        'h2',\n        'h3',\n        'h4',\n        'h5',\n        'h6',\n        'ul',\n        'ol',\n        'li',\n        'a',\n        'img',\n        'blockquote',\n        'code',\n        'pre'\n    ];\n    const allowedAttributes = {\n        'a': [\n            'href',\n            'title',\n            'target'\n        ],\n        'img': [\n            'src',\n            'alt',\n            'width',\n            'height'\n        ],\n        'blockquote': [\n            'cite'\n        ]\n    };\n    // إزالة العناصر الخطيرة\n    let sanitized = html.replace(/<script\\b[^<]*(?:(?!<\\/script>)<[^<]*)*<\\/script>/gi, '').replace(/<iframe\\b[^<]*(?:(?!<\\/iframe>)<[^<]*)*<\\/iframe>/gi, '').replace(/on\\w+=\"[^\"]*\"/gi, '') // إزالة event handlers\n    .replace(/javascript:/gi, '') // إزالة javascript: URLs\n    .replace(/data:/gi, ''); // إزالة data: URLs\n    return sanitized;\n}\nfunction sanitizeText(text) {\n    return text.replace(/[<>]/g, '') // إزالة أقواس HTML\n    .replace(/['\"]/g, '') // إزالة علامات الاقتباس\n    .trim();\n}\nfunction validateInput(input, type) {\n    if (!input || typeof input !== 'string') return false;\n    switch(type){\n        case 'email':\n            const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n            return emailRegex.test(input);\n        case 'text':\n            return input.length > 0 && input.length <= 1000;\n        case 'html':\n            return input.length > 0 && input.length <= 50000;\n        default:\n            return false;\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/sanitize.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fixArabicEncoding: () => (/* binding */ fixArabicEncoding),\n/* harmony export */   fixObjectEncoding: () => (/* binding */ fixObjectEncoding),\n/* harmony export */   supabase: () => (/* binding */ supabase)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n// هذا الملف يقوم بإعداد وتصدير \"العميل\" الخاص بـ Supabase\n// الذي سنستخدمه للتواصل مع قاعدة البيانات في كل مكان بالموقع\n\n// استيراد المفاتيح من ملف البيئة مع التحقق من وجودها\nconst supabaseUrl = \"https://zgktrwpladrkhhemhnni.supabase.co\" || 0;\nconst supabaseKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04\" || 0;\n// التحقق من وجود المتغيرات\nif (!supabaseUrl) {\n    throw new Error('Missing NEXT_PUBLIC_SUPABASE_URL environment variable');\n}\nif (!supabaseKey) {\n    throw new Error('Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable');\n}\n// دالة مساعدة لإصلاح encoding النص العربي\nfunction fixArabicEncoding(text) {\n    if (!text) return text;\n    try {\n        // إذا كان النص يحتوي على Unicode escape sequences\n        if (text.includes('\\\\u')) {\n            return JSON.parse(`\"${text}\"`);\n        }\n        return text;\n    } catch (error) {\n        console.warn('Failed to fix Arabic encoding for:', text);\n        return text;\n    }\n}\n// دالة لإصلاح encoding في كائن كامل\nfunction fixObjectEncoding(obj) {\n    if (!obj) return obj;\n    const fixed = {\n        ...obj\n    };\n    for(const key in fixed){\n        if (typeof fixed[key] === 'string') {\n            fixed[key] = fixArabicEncoding(fixed[key]);\n        } else if (Array.isArray(fixed[key])) {\n            fixed[key] = fixed[key].map((item)=>typeof item === 'string' ? fixArabicEncoding(item) : item);\n        }\n    }\n    return fixed;\n}\n// إنشاء وتصدير العميل مع إعدادات إضافية لدعم النص العربي\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, supabaseKey, {\n    db: {\n        schema: 'public'\n    },\n    auth: {\n        autoRefreshToken: true,\n        persistSession: true,\n        detectSessionInUrl: true\n    }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3N1cGFiYXNlLnRzIiwibWFwcGluZ3MiOiI7Ozs7Ozs7QUFBQSwwREFBMEQ7QUFDMUQsNkRBQTZEO0FBRVQ7QUFFcEQscURBQXFEO0FBQ3JELE1BQU1DLGNBQWNDLDBDQUFvQyxJQUFJLENBQTBDO0FBQ3RHLE1BQU1HLGNBQWNILGtOQUF5QyxJQUFJLENBQWtOO0FBRW5SLDJCQUEyQjtBQUMzQixJQUFJLENBQUNELGFBQWE7SUFDaEIsTUFBTSxJQUFJTSxNQUFNO0FBQ2xCO0FBRUEsSUFBSSxDQUFDRixhQUFhO0lBQ2hCLE1BQU0sSUFBSUUsTUFBTTtBQUNsQjtBQUVBLDBDQUEwQztBQUNuQyxTQUFTQyxrQkFBa0JDLElBQVk7SUFDNUMsSUFBSSxDQUFDQSxNQUFNLE9BQU9BO0lBRWxCLElBQUk7UUFDRixrREFBa0Q7UUFDbEQsSUFBSUEsS0FBS0MsUUFBUSxDQUFDLFFBQVE7WUFDeEIsT0FBT0MsS0FBS0MsS0FBSyxDQUFDLENBQUMsQ0FBQyxFQUFFSCxLQUFLLENBQUMsQ0FBQztRQUMvQjtRQUNBLE9BQU9BO0lBQ1QsRUFBRSxPQUFPSSxPQUFPO1FBQ2RDLFFBQVFDLElBQUksQ0FBQyxzQ0FBc0NOO1FBQ25ELE9BQU9BO0lBQ1Q7QUFDRjtBQUVBLG9DQUFvQztBQUM3QixTQUFTTyxrQkFBaURDLEdBQU07SUFDckUsSUFBSSxDQUFDQSxLQUFLLE9BQU9BO0lBRWpCLE1BQU1DLFFBQVE7UUFBRSxHQUFHRCxHQUFHO0lBQUM7SUFFdkIsSUFBSyxNQUFNRSxPQUFPRCxNQUFPO1FBQ3ZCLElBQUksT0FBT0EsS0FBSyxDQUFDQyxJQUFJLEtBQUssVUFBVTtZQUNsQ0QsS0FBSyxDQUFDQyxJQUFJLEdBQUdYLGtCQUFrQlUsS0FBSyxDQUFDQyxJQUFJO1FBQzNDLE9BQU8sSUFBSUMsTUFBTUMsT0FBTyxDQUFDSCxLQUFLLENBQUNDLElBQUksR0FBRztZQUNwQ0QsS0FBSyxDQUFDQyxJQUFJLEdBQUdELEtBQUssQ0FBQ0MsSUFBSSxDQUFDRyxHQUFHLENBQUMsQ0FBQ0MsT0FDM0IsT0FBT0EsU0FBUyxXQUFXZixrQkFBa0JlLFFBQVFBO1FBRXpEO0lBQ0Y7SUFFQSxPQUFPTDtBQUNUO0FBRUEseURBQXlEO0FBQ2xELE1BQU1NLFdBQVd4QixtRUFBWUEsQ0FBQ0MsYUFBYUksYUFBYTtJQUM3RG9CLElBQUk7UUFDRkMsUUFBUTtJQUNWO0lBQ0FDLE1BQU07UUFDSkMsa0JBQWtCO1FBQ2xCQyxnQkFBZ0I7UUFDaEJDLG9CQUFvQjtJQUN0QjtBQUVGLEdBQUciLCJzb3VyY2VzIjpbIkM6XFxVc2Vyc1xcaXNtYWlsXFw0XFxzcmNcXGxpYlxcc3VwYWJhc2UudHMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8g2YfYsNinINin2YTZhdmE2YEg2YrZgtmI2YUg2KjYpdi52K/Yp9ivINmI2KrYtdiv2YrYsSBcItin2YTYudmF2YrZhFwiINin2YTYrtin2LUg2KjZgCBTdXBhYmFzZVxyXG4vLyDYp9mE2LDZiiDYs9mG2LPYqtiu2K/ZhdmHINmE2YTYqtmI2KfYtdmEINmF2Lkg2YLYp9i52K/YqSDYp9mE2KjZitin2YbYp9iqINmB2Yog2YPZhCDZhdmD2KfZhiDYqNin2YTZhdmI2YLYuVxyXG5cclxuaW1wb3J0IHsgY3JlYXRlQ2xpZW50IH0gZnJvbSAnQHN1cGFiYXNlL3N1cGFiYXNlLWpzJ1xyXG5cclxuLy8g2KfYs9iq2YrYsdin2K8g2KfZhNmF2YHYp9iq2YrYrSDZhdmGINmF2YTZgSDYp9mE2KjZitim2Kkg2YXYuSDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYr9mH2KdcclxuY29uc3Qgc3VwYWJhc2VVcmwgPSBwcm9jZXNzLmVudi5ORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgfHwgJ2h0dHBzOi8vemdrdHJ3cGxhZHJraGhlbWhubmkuc3VwYWJhc2UuY28nXHJcbmNvbnN0IHN1cGFiYXNlS2V5ID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfU1VQQUJBU0VfQU5PTl9LRVkgfHwgJ2V5SmhiR2NpT2lKSVV6STFOaUlzSW5SNWNDSTZJa3BYVkNKOS5leUpwYzNNaU9pSnpkWEJoWW1GelpTSXNJbkpsWmlJNklucG5hM1J5ZDNCc1lXUnlhMmhvWlcxb2JtNXBJaXdpY205c1pTSTZJbUZ1YjI0aUxDSnBZWFFpT2pFM05USXdNamswTlRJc0ltVjRjQ0k2TWpBMk56WXdOVFExTW4wLnVIS2lzb2txazQ4NFZxNVFqQ2JWYmNkY2FieEFycnRLVU14amRDaWhlMDQnXHJcblxyXG4vLyDYp9mE2KrYrdmC2YIg2YXZhiDZiNis2YjYryDYp9mE2YXYqti62YrYsdin2KpcclxuaWYgKCFzdXBhYmFzZVVybCkge1xyXG4gIHRocm93IG5ldyBFcnJvcignTWlzc2luZyBORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwgZW52aXJvbm1lbnQgdmFyaWFibGUnKVxyXG59XHJcblxyXG5pZiAoIXN1cGFiYXNlS2V5KSB7XHJcbiAgdGhyb3cgbmV3IEVycm9yKCdNaXNzaW5nIE5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIGVudmlyb25tZW50IHZhcmlhYmxlJylcclxufVxyXG5cclxuLy8g2K/Yp9mE2Kkg2YXYs9in2LnYr9ipINmE2KXYtdmE2KfYrSBlbmNvZGluZyDYp9mE2YbYtSDYp9mE2LnYsdio2YpcclxuZXhwb3J0IGZ1bmN0aW9uIGZpeEFyYWJpY0VuY29kaW5nKHRleHQ6IHN0cmluZyk6IHN0cmluZyB7XHJcbiAgaWYgKCF0ZXh0KSByZXR1cm4gdGV4dDtcclxuXHJcbiAgdHJ5IHtcclxuICAgIC8vINil2LDYpyDZg9in2YYg2KfZhNmG2LUg2YrYrdiq2YjZiiDYudmE2YkgVW5pY29kZSBlc2NhcGUgc2VxdWVuY2VzXHJcbiAgICBpZiAodGV4dC5pbmNsdWRlcygnXFxcXHUnKSkge1xyXG4gICAgICByZXR1cm4gSlNPTi5wYXJzZShgXCIke3RleHR9XCJgKTtcclxuICAgIH1cclxuICAgIHJldHVybiB0ZXh0O1xyXG4gIH0gY2F0Y2ggKGVycm9yKSB7XHJcbiAgICBjb25zb2xlLndhcm4oJ0ZhaWxlZCB0byBmaXggQXJhYmljIGVuY29kaW5nIGZvcjonLCB0ZXh0KTtcclxuICAgIHJldHVybiB0ZXh0O1xyXG4gIH1cclxufVxyXG5cclxuLy8g2K/Yp9mE2Kkg2YTYpdi12YTYp9itIGVuY29kaW5nINmB2Yog2YPYp9im2YYg2YPYp9mF2YRcclxuZXhwb3J0IGZ1bmN0aW9uIGZpeE9iamVjdEVuY29kaW5nPFQgZXh0ZW5kcyBSZWNvcmQ8c3RyaW5nLCBhbnk+PihvYmo6IFQpOiBUIHtcclxuICBpZiAoIW9iaikgcmV0dXJuIG9iajtcclxuXHJcbiAgY29uc3QgZml4ZWQgPSB7IC4uLm9iaiB9IGFzIGFueTtcclxuXHJcbiAgZm9yIChjb25zdCBrZXkgaW4gZml4ZWQpIHtcclxuICAgIGlmICh0eXBlb2YgZml4ZWRba2V5XSA9PT0gJ3N0cmluZycpIHtcclxuICAgICAgZml4ZWRba2V5XSA9IGZpeEFyYWJpY0VuY29kaW5nKGZpeGVkW2tleV0gYXMgc3RyaW5nKTtcclxuICAgIH0gZWxzZSBpZiAoQXJyYXkuaXNBcnJheShmaXhlZFtrZXldKSkge1xyXG4gICAgICBmaXhlZFtrZXldID0gZml4ZWRba2V5XS5tYXAoKGl0ZW06IGFueSkgPT5cclxuICAgICAgICB0eXBlb2YgaXRlbSA9PT0gJ3N0cmluZycgPyBmaXhBcmFiaWNFbmNvZGluZyhpdGVtKSA6IGl0ZW1cclxuICAgICAgKTtcclxuICAgIH1cclxuICB9XHJcblxyXG4gIHJldHVybiBmaXhlZCBhcyBUO1xyXG59XHJcblxyXG4vLyDYpdmG2LTYp9ihINmI2KrYtdiv2YrYsSDYp9mE2LnZhdmK2YQg2YXYuSDYpdi52K/Yp9iv2KfYqiDYpdi22KfZgdmK2Kkg2YTYr9i52YUg2KfZhNmG2LUg2KfZhNi52LHYqNmKXHJcbmV4cG9ydCBjb25zdCBzdXBhYmFzZSA9IGNyZWF0ZUNsaWVudChzdXBhYmFzZVVybCwgc3VwYWJhc2VLZXksIHtcclxuICBkYjoge1xyXG4gICAgc2NoZW1hOiAncHVibGljJyxcclxuICB9LFxyXG4gIGF1dGg6IHtcclxuICAgIGF1dG9SZWZyZXNoVG9rZW46IHRydWUsXHJcbiAgICBwZXJzaXN0U2Vzc2lvbjogdHJ1ZSxcclxuICAgIGRldGVjdFNlc3Npb25JblVybDogdHJ1ZVxyXG4gIH1cclxuICAvLyDYpdiy2KfZhNipIGdsb2JhbCBoZWFkZXJzINmE2KrYrNmG2Kgg2KfZhNiq2K/Yp9iu2YQg2YXYuSDYsdmB2Lkg2KfZhNmF2YTZgdin2KpcclxufSk7XHJcbiJdLCJuYW1lcyI6WyJjcmVhdGVDbGllbnQiLCJzdXBhYmFzZVVybCIsInByb2Nlc3MiLCJlbnYiLCJORVhUX1BVQkxJQ19TVVBBQkFTRV9VUkwiLCJzdXBhYmFzZUtleSIsIk5FWFRfUFVCTElDX1NVUEFCQVNFX0FOT05fS0VZIiwiRXJyb3IiLCJmaXhBcmFiaWNFbmNvZGluZyIsInRleHQiLCJpbmNsdWRlcyIsIkpTT04iLCJwYXJzZSIsImVycm9yIiwiY29uc29sZSIsIndhcm4iLCJmaXhPYmplY3RFbmNvZGluZyIsIm9iaiIsImZpeGVkIiwia2V5IiwiQXJyYXkiLCJpc0FycmF5IiwibWFwIiwiaXRlbSIsInN1cGFiYXNlIiwiZGIiLCJzY2hlbWEiLCJhdXRoIiwiYXV0b1JlZnJlc2hUb2tlbiIsInBlcnNpc3RTZXNzaW9uIiwiZGV0ZWN0U2Vzc2lvbkluVXJsIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows","vendor-chunks/ramda","vendor-chunks/cookie"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fpages%2Froute&page=%2Fapi%2Fpages%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fpages%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();