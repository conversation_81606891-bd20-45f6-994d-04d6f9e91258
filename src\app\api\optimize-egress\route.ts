import { NextResponse } from 'next/server';
import { supabaseCache } from '@/lib/supabase-cache';

/**
 * API لمراقبة وتحسين استهلاك Egress
 */
export async function GET() {
  try {
    const cacheStats = supabaseCache.getStats();
    
    // إحصائيات الاستهلاك
    const stats = {
      cache: {
        size: cacheStats.size,
        maxSize: cacheStats.maxSize,
        hitRate: calculateHitRate(),
        keys: cacheStats.keys.slice(0, 10) // أول 10 مفاتيح فقط
      },
      optimization: {
        egressSavings: estimateEgressSavings(),
        recommendations: getOptimizationRecommendations()
      },
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(stats, {
      headers: {
        'Cache-Control': 'public, max-age=300' // 5 دقائق
      }
    });
  } catch (error) {
    console.error('Error in optimize-egress API:', error);
    return NextResponse.json({ 
      error: 'Failed to get optimization stats' 
    }, { status: 500 });
  }
}

/**
 * تنظيف Cache يدوياً
 */
export async function POST() {
  try {
    supabaseCache.clear();
    
    return NextResponse.json({ 
      success: true, 
      message: 'Cache cleared successfully',
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    console.error('Error clearing cache:', error);
    return NextResponse.json({ 
      error: 'Failed to clear cache' 
    }, { status: 500 });
  }
}

function calculateHitRate(): number {
  // حساب تقريبي لمعدل نجاح Cache
  // في التطبيق الحقيقي، ستحتاج لتتبع هذه الإحصائيات
  return 0.75; // 75% افتراضي
}

function estimateEgressSavings(): string {
  // تقدير توفير Egress بناءً على استخدام Cache
  const hitRate = calculateHitRate();
  const estimatedSavings = hitRate * 100;
  return `${estimatedSavings.toFixed(1)}% reduction in database queries`;
}

function getOptimizationRecommendations(): string[] {
  return [
    'Use specific column selection instead of SELECT *',
    'Implement pagination with LIMIT',
    'Cache frequently accessed data',
    'Use ISR for static content',
    'Optimize image sizes and formats'
  ];
}
