(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6348],{566:(e,r,s)=>{"use strict";s.r(r),s.d(r,{default:()=>c});var a=s(5155),t=s(2115),i=s(5695),n=s(6874),l=s.n(n);function c(e){let{params:r}=e,s=(0,i.useRouter)(),[n,c]=(0,t.useState)(!0),[o,d]=(0,t.useState)(!1),[u,m]=(0,t.useState)(null),[p,g]=(0,t.useState)(""),[x,h]=(0,t.useState)({name:"",description:"",short_description:"",category:"general",icon_url:"",image_url:"",pricing_type:"custom",pricing_amount:void 0,pricing_currency:"USD",status:"active",featured:!1,cta_text:"تعرف أكثر",cta_link:"",display_order:0,tags:[],features:[]}),[y,b]=(0,t.useState)(""),[f,j]=(0,t.useState)("");(0,t.useEffect)(()=>{(async()=>{g((await r).id)})()},[r]),(0,t.useEffect)(()=>{p&&v()},[p]);let v=async()=>{try{c(!0);let e=await fetch("/api/services/".concat(p));if(!e.ok){if(404===e.status)return void s.push("/admin/services");throw Error("فشل في جلب بيانات الخدمة")}let r=(await e.json()).service;m(r),h({name:r.name,description:r.description,short_description:r.short_description||"",category:r.category,icon_url:r.icon_url||"",image_url:r.image_url||"",pricing_type:r.pricing_type,pricing_amount:r.pricing_amount,pricing_currency:r.pricing_currency||"USD",status:r.status,featured:r.featured,cta_text:r.cta_text,cta_link:r.cta_link||"",display_order:r.display_order,tags:r.tags||[],features:r.features||[]}),b((r.tags||[]).join(", ")),j((r.features||[]).join("\n"))}catch(e){console.error("Error fetching service:",e),alert("حدث خطأ في جلب بيانات الخدمة"),s.push("/admin/services")}finally{c(!1)}},_=async e=>{e.preventDefault(),d(!0);try{let e=y.split(",").map(e=>e.trim()).filter(e=>e),r=f.split("\n").map(e=>e.trim()).filter(e=>e),a={...x,tags:e,features:r,pricing_amount:x.pricing_amount||null},t=await fetch("/api/services/".concat(p),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify(a)});if(!t.ok){let e=await t.json();throw Error(e.error||"فشل في تحديث الخدمة")}await t.json(),s.push("/admin/services")}catch(e){console.error("Error updating service:",e),alert("حدث خطأ في تحديث الخدمة: "+e.message)}finally{d(!1)}},N=e=>{let{name:r,value:s,type:a}=e.target;if("checkbox"===a){let s=e.target.checked;h(e=>({...e,[r]:s}))}else"number"===a?h(e=>({...e,[r]:s?parseFloat(s):void 0})):h(e=>({...e,[r]:s}))};return n?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,a.jsx)("p",{className:"text-gray-600",children:"جاري تحميل بيانات الخدمة..."})]})}):u?(0,a.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,a.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"تعديل الخدمة"}),(0,a.jsxs)("p",{className:"text-gray-600 mt-2",children:["تعديل بيانات الخدمة: ",u.name]})]}),(0,a.jsxs)("div",{className:"flex space-x-4 space-x-reverse",children:[(0,a.jsx)(l(),{href:"/services/".concat(u.id),target:"_blank",className:"bg-blue-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-blue-700 transition-colors duration-200",children:"معاينة الخدمة"}),(0,a.jsx)(l(),{href:"/admin/services",className:"bg-gray-600 text-white px-6 py-3 rounded-lg font-medium hover:bg-gray-700 transition-colors duration-200",children:"العودة للقائمة"})]})]}),(0,a.jsx)("div",{className:"bg-white rounded-lg shadow-sm",children:(0,a.jsxs)("form",{onSubmit:_,className:"p-6 space-y-6",children:[(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"name",className:"block text-sm font-medium text-gray-700 mb-2",children:"اسم الخدمة *"}),(0,a.jsx)("input",{type:"text",id:"name",name:"name",required:!0,value:x.name,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"أدخل اسم الخدمة"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"category",className:"block text-sm font-medium text-gray-700 mb-2",children:"التصنيف"}),(0,a.jsxs)("select",{id:"category",name:"category",value:x.category,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,a.jsx)("option",{value:"general",children:"عام"}),(0,a.jsx)("option",{value:"web-development",children:"تطوير مواقع"}),(0,a.jsx)("option",{value:"ai-solutions",children:"حلول الذكاء الاصطناعي"}),(0,a.jsx)("option",{value:"mobile-apps",children:"تطبيقات الجوال"}),(0,a.jsx)("option",{value:"consulting",children:"استشارات"}),(0,a.jsx)("option",{value:"design",children:"تصميم"})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"short_description",className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف المختصر"}),(0,a.jsx)("input",{type:"text",id:"short_description",name:"short_description",value:x.short_description,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"وصف مختصر للخدمة (اختياري)",maxLength:500})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"description",className:"block text-sm font-medium text-gray-700 mb-2",children:"الوصف التفصيلي *"}),(0,a.jsx)("textarea",{id:"description",name:"description",required:!0,rows:6,value:x.description,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"وصف تفصيلي للخدمة"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"icon_url",className:"block text-sm font-medium text-gray-700 mb-2",children:"رابط الأيقونة"}),(0,a.jsx)("input",{type:"url",id:"icon_url",name:"icon_url",value:x.icon_url,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"https://example.com/icon.png"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"image_url",className:"block text-sm font-medium text-gray-700 mb-2",children:"رابط الصورة الرئيسية"}),(0,a.jsx)("input",{type:"url",id:"image_url",name:"image_url",value:x.image_url,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"https://example.com/image.jpg"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricing_type",className:"block text-sm font-medium text-gray-700 mb-2",children:"نوع التسعير"}),(0,a.jsxs)("select",{id:"pricing_type",name:"pricing_type",value:x.pricing_type,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,a.jsx)("option",{value:"free",children:"مجاني"}),(0,a.jsx)("option",{value:"paid",children:"مدفوع"}),(0,a.jsx)("option",{value:"custom",children:"حسب الطلب"})]})]}),"paid"===x.pricing_type&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricing_amount",className:"block text-sm font-medium text-gray-700 mb-2",children:"السعر"}),(0,a.jsx)("input",{type:"number",id:"pricing_amount",name:"pricing_amount",min:"0",step:"0.01",value:x.pricing_amount||"",onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"0.00"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"pricing_currency",className:"block text-sm font-medium text-gray-700 mb-2",children:"العملة"}),(0,a.jsxs)("select",{id:"pricing_currency",name:"pricing_currency",value:x.pricing_currency,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,a.jsx)("option",{value:"USD",children:"دولار أمريكي (USD)"}),(0,a.jsx)("option",{value:"EUR",children:"يورو (EUR)"}),(0,a.jsx)("option",{value:"EGP",children:"جنيه مصري (EGP)"}),(0,a.jsx)("option",{value:"SAR",children:"ريال سعودي (SAR)"})]})]})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cta_text",className:"block text-sm font-medium text-gray-700 mb-2",children:"نص زر العمل"}),(0,a.jsx)("input",{type:"text",id:"cta_text",name:"cta_text",value:x.cta_text,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"تعرف أكثر"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"cta_link",className:"block text-sm font-medium text-gray-700 mb-2",children:"رابط زر العمل"}),(0,a.jsx)("input",{type:"text",id:"cta_link",name:"cta_link",value:x.cta_link,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"/page/contact-us"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"tags",className:"block text-sm font-medium text-gray-700 mb-2",children:"العلامات (مفصولة بفواصل)"}),(0,a.jsx)("input",{type:"text",id:"tags",value:y,onChange:e=>b(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"تقنية, ويب, تطوير"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"features",className:"block text-sm font-medium text-gray-700 mb-2",children:"المميزات (كل ميزة في سطر منفصل)"}),(0,a.jsx)("textarea",{id:"features",rows:4,value:f,onChange:e=>j(e.target.value),className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"ميزة 1 ميزة 2 ميزة 3"})]})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"status",className:"block text-sm font-medium text-gray-700 mb-2",children:"الحالة"}),(0,a.jsxs)("select",{id:"status",name:"status",value:x.status,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",children:[(0,a.jsx)("option",{value:"active",children:"نشط"}),(0,a.jsx)("option",{value:"inactive",children:"غير نشط"}),(0,a.jsx)("option",{value:"draft",children:"مسودة"})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{htmlFor:"display_order",className:"block text-sm font-medium text-gray-700 mb-2",children:"ترتيب العرض"}),(0,a.jsx)("input",{type:"number",id:"display_order",name:"display_order",min:"0",value:x.display_order,onChange:N,className:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent",placeholder:"0"})]}),(0,a.jsxs)("div",{className:"flex items-center",children:[(0,a.jsx)("input",{type:"checkbox",id:"featured",name:"featured",checked:x.featured,onChange:N,className:"h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"}),(0,a.jsx)("label",{htmlFor:"featured",className:"mr-2 block text-sm text-gray-900",children:"خدمة مميزة"})]})]}),(0,a.jsxs)("div",{className:"flex justify-end space-x-4 space-x-reverse pt-6 border-t",children:[(0,a.jsx)(l(),{href:"/admin/services",className:"bg-gray-300 text-gray-700 px-6 py-3 rounded-lg font-medium hover:bg-gray-400 transition-colors duration-200",children:"إلغاء"}),(0,a.jsx)("button",{type:"submit",disabled:o,className:"bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200 disabled:opacity-50 disabled:cursor-not-allowed",children:o?"جاري الحفظ...":"حفظ التغييرات"})]})]})})]})}):(0,a.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("p",{className:"text-red-600 mb-4",children:"لم يتم العثور على الخدمة"}),(0,a.jsx)(l(),{href:"/admin/services",className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90",children:"العودة للقائمة"})]})})}},5695:(e,r,s)=>{"use strict";var a=s(8999);s.o(a,"usePathname")&&s.d(r,{usePathname:function(){return a.usePathname}}),s.o(a,"useRouter")&&s.d(r,{useRouter:function(){return a.useRouter}})},9016:(e,r,s)=>{Promise.resolve().then(s.bind(s,566))}},e=>{var r=r=>e(e.s=r);e.O(0,[6874,8441,1684,7358],()=>r(9016)),_N_E=e.O()}]);