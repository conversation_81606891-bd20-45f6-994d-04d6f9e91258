{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "95f0372508e99b0d-MRS", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/ai_tools?category=eq.%C3%98%C2%A5%C3%99%C2%86%C3%98%C2%B4%C3%98%C2%A7%C3%98%C2%A1%20%C3%98%C2%A7%C3%99%C2%84%C3%98%C2%B5%C3%99%C2%88%C3%98%C2%B1&limit=3&order=rating.desc&select=%2A&slug=neq.midjourney&status=eq.published", "content-profile": "public", "content-range": "*/*", "content-type": "application/json; charset=utf-8", "date": "Mon, 14 Jul 2025 10:07:50 GMT", "sb-gateway-version": "1", "sb-project-ref": "zgktrwpladrkhhemhnni", "server": "cloudflare", "set-cookie": "__cf_bm=d2r8ZAHkVK8_j8t47C1bftOYD2FwDlsaIqaZXEGjnos-1752487670-*******-NmdQ5kMPmmxgbjbLir72icNNMYWoseu_HziGbZca_MgZVOKuvJQjoUE5S4tFfuurNVOzDDEv5zhNsbQHFz9leeleOszrG72Og9.jZ7CWeRo; path=/; expires=Mon, 14-Jul-25 10:37:50 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "9"}, "body": "W10=", "status": 200, "url": "https://zgktrwpladrkhhemhnni.supabase.co/rest/v1/ai_tools?select=*&status=eq.published&category=eq.%D8%A5%D9%86%D8%B4%D8%A7%D8%A1+%D8%A7%D9%84%D8%B5%D9%88%D8%B1&slug=neq.midjourney&order=rating.desc&limit=3"}, "revalidate": 604800, "tags": []}