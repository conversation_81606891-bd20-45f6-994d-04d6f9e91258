/**
 * نظام مراقبة استهلاك Supabase و Vercel
 */

interface UsageStats {
  supabase: {
    egress: number;
    databaseSize: number;
    storageSize: number;
    monthlyActiveUsers: number;
  };
  vercel: {
    isrWrites: number;
    functionInvocations: number;
    fastOriginTransfer: number;
    imageOptimization: number;
  };
  timestamp: string;
}

class UsageMonitor {
  private stats: UsageStats[] = [];
  private maxHistory = 100;

  addStats(stats: UsageStats) {
    this.stats.push(stats);
    
    // الحفاظ على حد أقصى للتاريخ
    if (this.stats.length > this.maxHistory) {
      this.stats = this.stats.slice(-this.maxHistory);
    }
  }

  getCurrentUsage(): UsageStats | null {
    return this.stats.length > 0 ? this.stats[this.stats.length - 1] : null;
  }

  getUsageTrend(hours: number = 24): UsageStats[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.stats.filter(stat => new Date(stat.timestamp) > cutoff);
  }

  predictUsage(days: number = 30): Partial<UsageStats> {
    if (this.stats.length < 2) {
      return {};
    }

    const recent = this.stats.slice(-24); // آخر 24 نقطة
    const avgGrowth = this.calculateAverageGrowth(recent);

    const current = this.getCurrentUsage();
    if (!current) return {};

    return {
      supabase: {
        egress: current.supabase.egress * (1 + avgGrowth) ** days,
        databaseSize: current.supabase.databaseSize * (1 + avgGrowth * 0.1) ** days,
        storageSize: current.supabase.storageSize * (1 + avgGrowth * 0.1) ** days,
        monthlyActiveUsers: current.supabase.monthlyActiveUsers * (1 + avgGrowth * 0.05) ** days,
      },
      vercel: {
        isrWrites: current.vercel.isrWrites * (1 + avgGrowth) ** days,
        functionInvocations: current.vercel.functionInvocations * (1 + avgGrowth) ** days,
        fastOriginTransfer: current.vercel.fastOriginTransfer * (1 + avgGrowth) ** days,
        imageOptimization: current.vercel.imageOptimization * (1 + avgGrowth) ** days,
      }
    };
  }

  private calculateAverageGrowth(stats: UsageStats[]): number {
    if (stats.length < 2) return 0;

    let totalGrowth = 0;
    let count = 0;

    for (let i = 1; i < stats.length; i++) {
      const prev = stats[i - 1];
      const curr = stats[i];

      // حساب معدل النمو للـ egress (المؤشر الأهم)
      if (prev.supabase.egress > 0) {
        const growth = (curr.supabase.egress - prev.supabase.egress) / prev.supabase.egress;
        totalGrowth += growth;
        count++;
      }
    }

    return count > 0 ? totalGrowth / count : 0;
  }

  getAlerts(): string[] {
    const current = this.getCurrentUsage();
    if (!current) return [];

    const alerts: string[] = [];

    // تحذيرات Supabase
    if (current.supabase.egress > 4000) { // 4GB من 5GB
      alerts.push('⚠️ Supabase Egress usage is high (>80%)');
    }

    if (current.supabase.databaseSize > 400) { // 400MB من 500MB
      alerts.push('⚠️ Database size is approaching limit');
    }

    // تحذيرات Vercel
    if (current.vercel.isrWrites > 160000) { // 160K من 200K
      alerts.push('🚨 ISR Writes usage is critical (>80%)');
    }

    if (current.vercel.functionInvocations > 800000) { // 800K من 1M
      alerts.push('⚠️ Function Invocations usage is high');
    }

    return alerts;
  }

  getOptimizationSuggestions(): string[] {
    const current = this.getCurrentUsage();
    if (!current) return [];

    const suggestions: string[] = [];

    // اقتراحات بناءً على الاستهلاك
    if (current.supabase.egress > 3000) {
      suggestions.push('Implement more aggressive caching');
      suggestions.push('Use SELECT with specific columns only');
      suggestions.push('Add pagination to large queries');
    }

    if (current.vercel.isrWrites > 100000) {
      suggestions.push('Increase ISR revalidation time');
      suggestions.push('Use static generation for unchanging content');
    }

    if (current.vercel.imageOptimization > 100) {
      suggestions.push('Optimize image sizes before upload');
      suggestions.push('Use external CDN for images');
    }

    return suggestions;
  }
}

// إنشاء instance واحد للتطبيق
export const usageMonitor = new UsageMonitor();

/**
 * دالة لتسجيل الاستهلاك الحالي
 */
export function recordUsage(stats: Partial<UsageStats>) {
  const fullStats: UsageStats = {
    supabase: {
      egress: 0,
      databaseSize: 0,
      storageSize: 0,
      monthlyActiveUsers: 0,
      ...stats.supabase
    },
    vercel: {
      isrWrites: 0,
      functionInvocations: 0,
      fastOriginTransfer: 0,
      imageOptimization: 0,
      ...stats.vercel
    },
    timestamp: new Date().toISOString()
  };

  usageMonitor.addStats(fullStats);
}
