{"kind": "FETCH", "data": {"headers": {"alt-svc": "h3=\":443\"; ma=86400", "cf-cache-status": "DYNAMIC", "cf-ray": "95f036e4486b45d4-MRS", "connection": "keep-alive", "content-encoding": "gzip", "content-location": "/articles?select=%2A&slug=eq.future-of-programming-will-no-code-replace-developers&status=eq.published", "content-profile": "public", "content-range": "0-0/*", "content-type": "application/vnd.pgrst.object+json; charset=utf-8", "date": "Mon, 14 Jul 2025 10:07:40 GMT", "sb-gateway-version": "1", "sb-project-ref": "zgktrwpladrkhhemhnni", "server": "cloudflare", "set-cookie": "__cf_bm=WtIPRZzFU8uRheI6tyOBoLUZ49LqUL1MI8ol5fYLcJ0-1752487660-*******-TfmiV0Gl4au90P_ZAuZoP4LjO8qYaxIwQHd3oQGE118sD9IS0H5BwbnVpQvI1zLZiQ3nNafVIuopNgsEl52PiywacvY2yQd2nVCP7Jzf2Fo; path=/; expires=Mon, 14-Jul-25 10:37:40 GMT; domain=.supabase.co; HttpOnly; Secure; SameSite=None", "strict-transport-security": "max-age=31536000; includeSubDomains; preload", "transfer-encoding": "chunked", "vary": "Accept-Encoding", "x-content-type-options": "nosniff", "x-envoy-attempt-count": "1", "x-envoy-upstream-service-time": "3"}, "body": "eyJpZCI6ImI4YzlkMGUxLWYyYTMtNDU2Ny04OTAxLWNkZWYxMjM0NTY3OCIsInRpdGxlIjoi2YXYs9iq2YLYqNmEINin2YTYqNix2YXYrNipOiDZh9mEINiz2KrYrdmEINij2K/ZiNin2KogTm8tQ29kZSDZhdit2YQg2KfZhNmF2KjYsdmF2KzZitmG2J8iLCJzbHVnIjoiZnV0dXJlLW9mLXByb2dyYW1taW5nLXdpbGwtbm8tY29kZS1yZXBsYWNlLWRldmVsb3BlcnMiLCJleGNlcnB0Ijoi2YbZgtin2LQg2LnZhdmK2YIg2K3ZiNmEINmF2LPYqtmC2KjZhCDZhdmH2YbYqSDYp9mE2KjYsdmF2KzYqSDZgdmKINi42YQg2LXYudmI2K8g2KPYr9mI2KfYqiBOby1Db2RlLiDZh9mEINmH2Yog2YbZh9in2YrYqSDYp9mE2YXYqNix2YXYrNmK2YYg2KPZhSDYqti32YjYsSDYt9io2YrYudmKINmE2KPYr9mI2KfYsdmH2YXYnyDYqtit2YTZitmEINmE2YTYp9iq2KzYp9mH2KfYqiDYp9mE2K3Yp9mE2YrYqSDZiNin2YTZhdiz2KrZgtio2YTZitipLiIsImNvbnRlbnQiOiJ7XCJibG9ja3NcIjogW3tcImRhdGFcIjoge1widGV4dFwiOiBcItmF2YLYr9mF2Kk6INin2YTYs9ik2KfZhCDYp9mE2LDZiiDZiti02LrZhCDYqNin2YQg2KfZhNis2YXZiti5XCIsIFwibGV2ZWxcIjogMn0sIFwidHlwZVwiOiBcImhlYWRlclwifSwge1wiZGF0YVwiOiB7XCJ0ZXh0XCI6IFwi2YXYuSDZg9mEINmC2YHYstipINiq2YPZhtmI2YTZiNis2YrYqSDZg9io2LHZidiMINmK2LjZh9ixINiz2KTYp9mEINit2KrZhdmKOiDZhdinINmH2Yog2KfZhNmI2LjYp9im2YEg2KfZhNiq2Yog2LPYqtiu2KrZgdmK2J8g2YjYudmG2K/ZhdinINmK2KrYudmE2YIg2KfZhNij2YXYsSDYqNi12LnZiNivINij2K/ZiNin2KogTm8tQ29kZSDYp9mE2YXYsNmH2YTYjCDZgdil2YYg2KfZhNiz2KTYp9mEINin2YTYsNmKINmK2KrYsdiv2K8g2YHZiiDYo9ix2YjZgtipINi02LHZg9in2Kog2KfZhNiq2YPZhtmI2YTZiNis2YrYpyDZiNin2YTZhdmG2KrYr9mK2KfYqiDYp9mE2YXYqtiu2LXYtdipINmH2Yg6IFxcXCLZh9mEINiz2KrYrdmEINij2K/ZiNin2KogTm8tQ29kZSDZhdit2YQg2KfZhNmF2KjYsdmF2KzZitmG2J9cXFwiLiDZh9iw2Kcg2KfZhNiz2KTYp9mEINmK2KvZitixINmC2YTZgiDYp9mE2YXYqNix2YXYrNmK2YYg2KfZhNit2KfZhNmK2YrZhtiMINmI2YrYrNi52YQg2KfZhNi32YXZiNit2YrZhiDYp9mE2KzYr9ivINmK2KrYs9in2KHZhNmI2YYg2LnZhdinINil2LDYpyDZg9in2YYg2KfYs9iq2KvZhdin2LEg2LPZhtmI2KfYqiDZgdmKINiq2LnZhNmFINin2YTYqNix2YXYrNipINmE2Kcg2YrYstin2YQg2YLYsdin2LHZi9inINit2YPZitmF2YvYpy4g2YXZhiDYrNmH2KnYjCDZhtix2Ykg2KPYr9mI2KfYqiDYqtiq2YrYrSDZhNi62YrYsSDYp9mE2KrZgtmG2YrZitmGINio2YbYp9ihINiq2LfYqNmK2YLYp9iqINmF2LnZgtiv2Kkg2YPYp9mG2Kog2KrYqti32YTYqCDZgdix2YLZi9inINmD2KfZhdmE2Kkg2YXZhiDYp9mE2YXYt9mI2LHZitmGLiDZiNmF2YYg2KzZh9ipINij2K7YsdmJ2Iwg2YTYpyDZitiy2KfZhCDYp9mE2LnYp9mE2YUg2KfZhNix2YLZhdmKINmK2LnYqtmF2K8g2KjYtNmD2YQg2KPYs9in2LPZiiDYudmE2Ykg2KjZhtmK2Kkg2KrYrdiq2YrYqSDZiNij2YbYuNmF2Kkg2YXYudmC2K/YqSDYqtmFINio2YbYp9ik2YfYpyDYqNiz2YjYp9i52K8g2KfZhNmF2KjYsdmF2KzZitmGLiDZgdmKINmH2LDYpyDYp9mE2YXZgtin2YTYjCDYs9mG2LrZiNi1INio2LnZhdmCINmB2Yog2YfYsNinINin2YTYs9ik2KfZhCDYp9mE2KzYr9mE2YouINmE2YYg2YbZgtiv2YUg2KXYrNin2KjYqSDYqNiz2YrYt9ipINio2YAgXFxcItmG2LnZhVxcXCIg2KPZiCBcXFwi2YTYp1xcXCLYjCDYqNmEINiz2YbYrdmE2YQg2KfZhNiv2YrZhtin2YXZitmD2YrYp9iqINin2YTZhdiq2LrZitix2Kkg2YHZiiDYs9mI2YIg2KfZhNi52YXZhNiMINmI2YbYs9iq2YPYtNmBINmD2YrZgSDYo9mGINi12LnZiNivINin2YTZgCBOby1Db2RlINmE2Kcg2YrYudmG2Yog2KjYp9mE2LbYsdmI2LHYqSDZhtmH2KfZitipINin2YTZhdio2LHZhdis2Iwg2KjZhCDZgtivINmK2YPZiNmGINio2K/Yp9mK2Kkg2YTYqti32YjYsSDYr9mI2LHZhyDYpdmE2Ykg2LTZitihINis2K/ZitivINmI2KPZg9ir2LEg2KfYs9iq2LHYp9iq2YrYrNmK2KkuXCJ9LCBcInR5cGVcIjogXCJwYXJhZ3JhcGhcIn0sIHtcImRhdGFcIjoge1widGV4dFwiOiBcItin2YTZhdmH2KfZhSDYp9mE2KrZiiDZitiz2KrZh9iv2YHZh9inINin2YTZgCBOby1Db2RlINit2KfZhNmK2YvYp1wiLCBcImxldmVsXCI6IDN9LCBcInR5cGVcIjogXCJoZWFkZXJcIn0sIHtcImRhdGFcIjoge1widGV4dFwiOiBcItmE2YPZiiDZhtmB2YfZhSDYp9mE2KrYo9ir2YrYsSDYp9mE2K3ZgtmK2YLZitiMINmK2KzYqCDYo9mGINmG2LnYsdmBINmF2Kcg2KfZhNiw2Yog2KrZgdi52YTZhyDYo9iv2YjYp9iqIE5vLUNvZGUg2KjZg9mB2KfYodipINin2YTZitmI2YUuINil2YbZh9inINiq2KrZgdmI2YIg2YHZiiDYo9iq2YXYqtipINmI2KfYs9iq2KjYr9in2YQg2KfZhNmF2YfYp9mFINin2YTYqtmKINmD2KfZhtiqINiq2LnYqtio2LEg2YHZiiDZitmI2YUg2YXZhiDYp9mE2KPZitin2YUgXFxcItin2YTYrtio2LIg2YjYp9mE2LLYqNiv2KlcXFwiINmE2YTZhdi32YjYsdmK2YYg2KfZhNmF2KjYqtiv2KbZitmGINij2Ygg2YXYt9mI2LHZiiDYp9mE2YjYp9is2YfYp9iqINin2YTYo9mF2KfZhdmK2KkuINmI2KrYtNmF2YQg2YfYsNmHINin2YTZhdmH2KfZhTogICogKirYqNmG2KfYoSDYp9mE2YXZiNin2YLYuSDYp9mE2KrYudix2YrZgdmK2Kkg2YjYp9mE2YXYr9mI2YbYp9iqOioqINij2K/ZiNin2Kog2YXYq9mEIFdlYmZsb3cg2YggV2l4INij2KrZhdiq2Kog2YfYsNmHINin2YTYudmF2YTZitipINio2KfZhNmD2KfZhdmEINiq2YLYsdmK2KjZi9inLiAgKiAqKtil2YbYtNin2KEg2LXZgdit2KfYqiDYp9mE2YfYqNmI2LcgKExhbmRpbmcgUGFnZXMpOioqINmK2YXZg9mGINmE2YXYr9mK2LEg2KfZhNiq2LPZiNmK2YIg2KfZhNii2YYg2KjZhtin2KEg2YjYp9iu2KrYqNin2LEg2LXZgdit2KfYqiDYp9mE2YfYqNmI2Lcg2KjZhtmB2LPZhyDYqNin2LPYqtiu2K/Yp9mFINij2K/ZiNin2Kog2YXYq9mEIENhcnJkINij2YggSW5zdGFwYWdlLiAgKiAqKtiq2LfZiNmK2LEg2KfZhNmG2YXYp9iw2Kwg2KfZhNij2YjZhNmK2KkgKE1WUHMpOioqINio2K/ZhNin2Ysg2YXZhiDYo9mGINmK2YLYttmKINin2YTZhdio2LHZhdis2YjZhiDYo9iz2KfYqNmK2Lkg2YHZiiDYqNmG2KfYoSDZhtiz2K7YqSDYo9mI2YTZitip2Iwg2YrZhdmD2YYg2YTZhdik2LPYsyDYp9mE2LTYsdmD2Kkg2KfZhNii2YYg2KjZhtin2KTZh9inINio2YbZgdiz2Ycg2LnZhNmJIEJ1YmJsZSDZgdmKINij2YrYp9mFLiAgKiAqKtio2YbYp9ihINin2YTYo9iv2YjYp9iqINin2YTYr9in2K7ZhNmK2Kk6Kiog2YrZhdmD2YYg2YTZhdiv2YrYsSDYp9mE2YXZiNin2LHYryDYp9mE2KjYtNix2YrYqSDYqNmG2KfYoSDYqti32KjZitmCINmE2KXYr9in2LHYqSDYt9mE2KjYp9iqINin2YTYpdis2KfYstin2Kog2KjYp9iz2KrYrtiv2KfZhSBTb2Z0ciDZiCBBaXJ0YWJsZSDYqNiv2YTYp9mLINmF2YYg2KXYsti52KfYrCDZgtiz2YUg2KrZg9mG2YjZhNmI2KzZitinINin2YTZhdi52YTZiNmF2KfYqi4gICogKirYp9mE2KPYqtmF2KrYqSDYp9mE2KjYs9mK2LfYqSDZiNin2YTZhdiq2YjYs9i32Kk6Kiog2LHYqNi3INin2YTYqti32KjZitmC2KfYqiDYqNio2LnYttmH2Kcg2KfZhNio2LnYttiMINmI2YfZiiDZhdmH2YXYqSDZg9in2YbYqiDYqtiq2LfZhNioINmD2KrYp9io2Kkg2LPZg9ix2KjYqtin2KogQVBJ2Iwg2KPYtdio2K3YqiDYp9mE2KLZhiDYs9mH2YTYqSDZhdi5IFphcGllciDZiCBNYWtlLiAg2YbZhNin2K3YuCDYo9mGINmD2YQg2YfYsNmHINin2YTZhdmH2KfZhSDYqtmC2Lkg2YHZiiDZhti32KfZgiDYp9mE2KrYt9io2YrZgtin2Kog2KfZhNmC2YrYp9iz2YrYqSDZiNin2YTZhdiq2YPYsdix2KkuINin2YTZgCBOby1Db2RlINmK2K3ZhCDYp9mE2YXYtNin2YPZhCDYp9mE2KrZiiDYqtmFINit2YTZh9inINii2YTYp9mBINin2YTZhdix2KfYqiDZhdmGINmC2KjZhC5cIn0sIFwidHlwZVwiOiBcInBhcmFncmFwaFwifSwge1wiZGF0YVwiOiB7XCJ0ZXh0XCI6IFwi2KfZhNmF2KzYp9mE2KfYqiDYp9mE2KrZiiDZhNinINmK2LLYp9mEINmB2YrZh9inINin2YTZg9mI2K8g2YXZhNmD2YvYpyAo2YjZhNmGINmK2KrYutmK2LEg2LDZhNmDINmC2LHZitio2YvYpylcIiwgXCJsZXZlbFwiOiAzfSwgXCJ0eXBlXCI6IFwiaGVhZGVyXCJ9LCB7XCJkYXRhXCI6IHtcInRleHRcIjogXCLYudmE2Ykg2KfZhNis2KfZhtioINin2YTYotiu2LHYjCDZh9mG2KfZgyDZhdis2KfZhNin2Kog2YjYp9iz2LnYqSDZiNmF2LnZgtiv2Kkg2K3ZitirINmE2Kcg2YrYstin2YQg2KfZhNmD2YjYryDZh9mIINin2YTYo9iv2KfYqSDYp9mE2YjYrdmK2K/YqSDYp9mE2YLYp9iv2LHYqSDYudmE2Ykg2KXZhtis2KfYsiDYp9mE2YXZh9mF2KkuINmH2LDZhyDYp9mE2YXYrNin2YTYp9iqINmH2Yog2KfZhNiq2Yog2LPZitiy2K/Yp9ivINmB2YrZh9inINin2YTYt9mE2Kgg2LnZhNmJINin2YTZhdio2LHZhdis2YrZhiDYp9mE2YXZh9ix2Kk6ICAqICoq2KjZhtin2KEg2KfZhNio2YbZitipINin2YTYqtit2KrZitipINin2YTYo9iz2KfYs9mK2Kk6Kiog2YXZhiDYp9mE2LDZiiDZitio2YbZiiDZhdmG2LXYp9iqINin2YTZgCBOby1Db2RlINmG2YHYs9mH2KfYnyDYp9mE2YXYqNix2YXYrNmI2YYuINmF2YYg2KfZhNiw2Yog2YrYqNmG2Yog2YjZiti12YjZhiDYo9mG2LjZhdipINin2YTYqti02LrZitmE2Iwg2YjZgtmI2KfYudivINin2YTYqNmK2KfZhtin2Kog2KfZhNi22K7Zhdip2Iwg2YjYtNio2YPYp9iqINiq2YjYtdmK2YQg2KfZhNmF2K3YqtmI2YkgKENETnMpINin2YTYqtmKINmK2LnYqtmF2K8g2LnZhNmK2YfYpyDYp9mE2KXZhtiq2LHZhtiqINio2KPZg9mF2YTZh9ifINin2YTZhdio2LHZhdis2YjZhi4gICogKirYp9mE2K7ZiNin2LHYstmF2YrYp9iqINin2YTZhdiu2LXYtdipINmI2KfZhNij2K/Yp9ihINin2YTZgdin2KbZgjoqKiDYo9mKINiq2LfYqNmK2YIg2YrYqti32YTYqCDZhdi52KfZhNis2Kkg2KjZitin2YbYp9iqINmB2Yog2KfZhNmI2YLYqiDYp9mE2YHYudmE2YrYjCDYo9mIINiu2YjYp9ix2LLZhdmK2KfYqiDYqti52YTZhSDYotmE2Kkg2YXYqti32YjYsdip2Iwg2KPZiCDYo9iv2KfYodmLINmB2KfYptmCINin2YTYs9ix2LnYqSAo2YXYq9mEINij2YbYuNmF2Kkg2KfZhNiq2K/Yp9mI2YQg2KfZhNmF2KfZhNmKINij2Ygg2YXYrdix2YPYp9iqINin2YTYo9mE2LnYp9ioKSDZitis2Kgg2KPZhiDZitmP2KjZhtmJINio2KfZhNmD2YjYry4gICogKirYp9mE2KPZhdin2YYg2YjYp9mE2KrZiNiz2Lkg2LnZhNmJINmG2LfYp9mCINi22K7ZhToqKiDYqNmG2KfYoSDYo9mG2LjZhdipINii2YXZhtipINmI2YLYp9iv2LHYqSDYudmE2Ykg2K7Yr9mF2Kkg2YXYptin2Kog2KfZhNmF2YTYp9mK2YrZhiDZhdmGINin2YTZhdiz2KrYrtiv2YXZitmGINmK2KrYt9mE2Kgg2K7YqNix2Kkg2LnZhdmK2YLYqSDZgdmKINmH2YbYr9iz2Kkg2KfZhNio2LHZhdis2YrYp9iqINmE2Kcg2YrZhdmD2YYg2YTYo9iv2YjYp9iqIE5vLUNvZGUg2KrZiNmB2YrYsdmH2Kcg2K3Yp9mE2YrZi9inLiAgKiAqKtin2YTYqtmD2KfZhdmE2KfYqiDYp9mE2YXYudmC2K/YqToqKiDYqNmK2YbZhdinINmK2LPZh9mEIE5vLUNvZGUg2LHYqNi3INin2YTYqti32KjZitmC2KfYqiDYp9mE2LTYp9im2LnYqdiMINmB2KXZhiDYsdio2Lcg2YbYuNin2YUg2K3Yr9mK2Ksg2YXYuSDZhti42KfZhSDZgtiv2YrZhSAoTGVnYWN5IFN5c3RlbSkg2YHZiiDYtNix2YPYqSDZg9io2YrYsdipINmK2KrYt9mE2Kgg2K/Yp9im2YXZi9inINmD2KrYp9io2Kkg2YPZiNivINmF2K7Ytdi1LlwifSwgXCJ0eXBlXCI6IFwicGFyYWdyYXBoXCJ9LCB7XCJkYXRhXCI6IHtcInRleHRcIjogXCLYqti32YjYsSDYr9mI2LEg2KfZhNmF2KjYsdmF2Kw6INmF2YYg2KjZhtmR2KfYoSDYpdmE2Ykg2YXZh9mG2K/YsyDZhdi52YXYp9ix2YpcIiwgXCJsZXZlbFwiOiAzfSwgXCJ0eXBlXCI6IFwiaGVhZGVyXCJ9LCB7XCJkYXRhXCI6IHtcInRleHRcIjogXCLYpdiw2YvYp9iMINin2YTZgCBOby1Db2RlINmE2YYg2YrYrdmEINmF2K3ZhCDYp9mE2YXYqNix2YXYrNmK2YbYjCDYqNmEINiz2YrYutmK2LEg2LfYqNmK2LnYqSDYudmF2YTZh9mFLiDYs9mK2KrZhSDYqtit2LHZitixINin2YTZhdio2LHZhdis2YrZhiDZhdmGINin2YTZhdmH2KfZhSDYp9mE2LHZiNiq2YrZhtmK2Kkg2YjYp9mE2YXZhdmE2KkgKNmF2KvZhCDYqNmG2KfYoSDZhtmF2KfYsNisIENSVUQg2KjYs9mK2LfYqSDYo9mIINiq2LXZhdmK2YUg2LXZgdit2KfYqiDYq9in2KjYqtipKSDZhNmE2KrYsdmD2YrYsiDYudmE2Ykg2KfZhNmF2LTYp9mD2YQg2KfZhNij2YPYq9ixINiq2K3Yr9mK2YvYpyDZiNmC2YrZhdipLiDZitmF2YPZhtmG2Kcg2KrYtNio2YrZhyDYsNmE2YMg2KjYuNmH2YjYsSDYp9mE2KLZhNin2Kog2KfZhNit2KfYs9io2KnYmyDZgdmH2Yog2YTZhSDYqtmC2LbZkCDYudmE2Ykg2LnZhNmF2KfYoSDYp9mE2LHZitin2LbZitin2KrYjCDYqNmEINit2LHYsdiq2YfZhSDZhdmGINin2YTYrdiz2KfYqNin2Kog2KfZhNmK2K/ZiNmK2Kkg2KfZhNmF2YXZhNipINmE2YTYqtix2YPZitiyINi52YTZiSDYp9mE2YbYuNix2YrYp9iqINmI2KfZhNmF2YHYp9mH2YrZhSDYp9mE2KPYudmE2Ykg2YXYs9iq2YjZiS4gICoq2LPZitiq2K3ZiNmEINiv2YjYsSDYp9mE2YXYqNix2YXYrCDYp9mE2YXYs9iq2YLYqNmE2Yog2YTZitix2YPYsiDYudmE2Yk6KiogKiAqKtmH2YbYr9iz2Kkg2KfZhNmG2LjZhToqKiDYqti12YXZitmFINin2YTYqNmG2YrYqSDYp9mE2YPZhNmK2Kkg2YTZhNiq2LfYqNmK2YLYp9iqINin2YTZhdi52YLYr9ip2Iwg2YjYqtit2K/ZitivINij2Yog2KzYstihINmK2YXZg9mGINio2YbYp9ik2Ycg2KjZgCBOby1Db2RlINmI2KPZiiDYrNiy2KEg2YrYqti32YTYqCDZg9mI2K/Zi9inINmF2K7Ytdi12YvYpy4gICogKirYqNmG2KfYoSDZiNiq2YjYs9mK2Lkg2YXZhti12KfYqiBOby1Db2RlOioqINin2YTYudmF2YQg2YHZiiDYtNix2YPYp9iqINmF2KvZhCBCdWJibGUg2YggV2ViZmxvdyDZhNio2YbYp9ihINin2YTYrNmK2YQg2KfZhNmC2KfYr9mFINmF2YYg2YfYsNmHINin2YTYo9iv2YjYp9iqLiAgKiAqKtil2YbYtNin2KEgXFxcItmF2YPZiNmG2KfYqlxcXCIg2YLYp9io2YTYqSDZhNil2LnYp9iv2Kkg2KfZhNin2LPYqtiu2K/Yp9mFOioqINio2YbYp9ihINil2LbYp9mB2KfYqiAoUGx1Z2lucykg2YjZhdmD2YjZhtin2Kog2YXYudmC2K/YqSDYqNin2YTZg9mI2K/YjCDZitmF2YPZhiDZhNmF2LfZiNix2YogTm8tQ29kZSDYp9iz2KrYrtiv2KfZhdmH2Kcg2KjYudivINiw2YTZgyDZgdmKINmF2LTYp9ix2YrYudmH2YUuICjYudmE2Ykg2LPYqNmK2YQg2KfZhNmF2KvYp9mE2Iwg2KjZhtin2KEg2KXYttin2YHYqSDZhdi52YLYr9ipINmE2YXYudin2YTYrNipINin2YTYr9mB2Lkg2YTZgCBCdWJibGUpLiAgKiAqKtin2YTYo9mF2YYg2YjYp9mE2KPYr9in2KE6Kiog2KfZhNiq2LHZg9mK2LIg2LnZhNmJINiq2KPZhdmK2YYg2KfZhNij2YbYuNmF2Kkg2YjYqtit2LPZitmGINij2K/Yp9im2YfYp9iMINmI2YfZiiDZhdmH2KfZhSDYqtiq2LfZhNioINmB2YfZhdmL2Kcg2LnZhdmK2YLZi9inINmE2YTZg9mI2K8uICDYqNin2K7Yqti12KfYsdiMINiz2YrZhtiq2YLZhCDYp9mE2YXYqNix2YXYrCDZhdmGINmD2YjZhtmHIFxcXCLYudin2YXZhCDYqNmG2KfYoVxcXCIg2KXZhNmJIFxcXCLZhdmH2YbYr9izINmF2LnZhdin2LHZilxcXCIg2YggXFxcIti12KfZhti5INij2K/ZiNin2KpcXFwiLiDYs9iq2YLZhCDZgtmK2YXYqSDYp9mE2YXYqNix2YXYrCDYp9mE2LDZiiDZg9mEINmF2Kcg2YrYudix2YHZhyDZh9mIINmD2YrZgdmK2Kkg2KjZhtin2KEg2YjYp9is2YfYqSDYo9mF2KfZhdmK2Kkg2KjYs9mK2LfYqdiMINio2YrZhtmF2Kcg2LPYqtiy2K/Yp9ivINmC2YrZhdipINin2YTZhdio2LHZhdisINin2YTYsNmKINmK2YHZh9mFINio2YbZitipINin2YTZhti42YUg2KfZhNmF2LnZgtiv2KkuXCJ9LCBcInR5cGVcIjogXCJwYXJhZ3JhcGhcIn0sIHtcImRhdGFcIjoge1widGV4dFwiOiBcItmG2LXZitit2Kkg2YTZhNmF2KjYsdmF2KzZitmGINin2YTYrdin2YTZitmK2YYg2YjYp9mE2YXYs9iq2YLYqNmE2YrZitmGXCIsIFwibGV2ZWxcIjogM30sIFwidHlwZVwiOiBcImhlYWRlclwifSwge1wiZGF0YVwiOiB7XCJ0ZXh0XCI6IFwi2YTYpyDYqtiu2YEg2YXZhiDYp9mE2YAgTm8tQ29kZdiMINio2YQg2KfYrdiq2LbZhtmHLiAgKiAqKtiq2LnZhNmFINij2K/ZiNin2KogTm8tQ29kZToqKiDYp9iz2KrYrtiv2YXZh9inINmE2KrYs9ix2YrYuSDYudmF2YTZgy4g2YLZhSDYqNio2YbYp9ihINin2YTZhtmF2KfYsNisINin2YTYo9mI2YTZitipINmE2LnZhdmE2KfYptmDINio2KfYs9iq2K7Yr9in2YUgQnViYmxlINmE2KrZiNmB2YrYsSDYp9mE2YjZgtiqLiDZgtmFINio2KPYqtmF2KrYqSDZhdmH2KfZhdmDINio2KfYs9iq2K7Yr9in2YUgTWFrZS4gICogKirYsdmD2LIg2LnZhNmJINin2YTYo9iz2KfYs9mK2KfYqjoqKiDZhNinINiq2KrYudmE2YUg2YHZgti3INil2LfYp9ixINi52YXZhCAoRnJhbWV3b3JrKSDYudi12LHZitiMINio2YQg2KrYudmE2YUg2KfZhNmF2KjYp9iv2KYg2KfZhNij2LPYp9iz2YrYqSDZhNi52YTZiNmFINin2YTZg9mF2KjZitmI2KrYsTog2YfZitin2YPZhCDYp9mE2KjZitin2YbYp9iq2Iwg2KfZhNiu2YjYp9ix2LLZhdmK2KfYqtiMINiq2LXZhdmK2YUg2YLZiNin2LnYryDYp9mE2KjZitin2YbYp9iq2Iwg2YjZhdio2KfYr9imINmH2YbYr9iz2Kkg2KfZhNio2LHZhdis2YrYp9iqLiDZh9iw2Ycg2KfZhNmF2YfYp9ix2KfYqiDZhNinINiq2YXZiNiqINij2KjYr9mL2KcuICAqICoq2KrYrti12LUg2YHZiiDZhdis2KfZhCDZhdi52YLYrzoqKiDYqtiu2LXYtSDZgdmKINmF2KzYp9mEINmF2KvZhCDYp9mE2LDZg9in2KEg2KfZhNin2LXYt9mG2KfYudmK2Iwg2KfZhNij2YXZhiDYp9mE2LPZitio2LHYp9mG2YrYjCDYo9mIINiq2LfZiNmK2LEg2KfZhNij2YbYuNmF2Kkg2KfZhNmF2YjYsti52KkuINmH2LDZhyDZh9mKINin2YTZhdis2KfZhNin2Kog2KfZhNiq2Yog2LPYqtit2KfZgdi4INi52YTZiSDZgtmK2YXYqtmH2Kcg2K/Yp9im2YXZi9inLiAgKiAqKti32YjYsSDZhdmH2KfYsdin2KrZgyDYp9mE2YbYp9i52YXYqToqKiDZgtiv2LHYqtmDINi52YTZiSDYp9mE2KrZiNin2LXZhNiMINmB2YfZhSDZhdiq2LfZhNio2KfYqiDYp9mE2LnZhdmE2Iwg2YjZgtmK2KfYr9ipINin2YTZhdi02KfYsdmK2Lkg2LPYqti12KjYrSDYo9mD2KvYsSDYo9mH2YXZitipINmF2YYg2YLYr9ix2KrZgyDYudmE2Ykg2YPYqtin2KjYqSDYp9mE2YPZiNivINio2LPYsdi52KkuXCJ9LCBcInR5cGVcIjogXCJwYXJhZ3JhcGhcIn0sIHtcImRhdGFcIjoge1widGV4dFwiOiBcItiu2KfYqtmF2Kk6INmE2YrYsyDYp9iz2KrYqNiv2KfZhNmL2KfYjCDYqNmEINiq2LfZiNix2YvYp1wiLCBcImxldmVsXCI6IDJ9LCBcInR5cGVcIjogXCJoZWFkZXJcIn0sIHtcImRhdGFcIjoge1widGV4dFwiOiBcItil2KzYp9io2Kkg2LnZhNmJINin2YTYs9ik2KfZhCDYp9mE2KPYtdmE2Yo6INmE2KfYjCDYo9iv2YjYp9iqIE5vLUNvZGUg2YTZhiDYqtit2YQg2YXYrdmEINin2YTZhdio2LHZhdis2YrZhi4g2YTZg9mG2YfYpyDYs9iq2K3ZhCDZhdit2YQg2KfZhNmF2YfYp9mFINin2YTZhdiq2YPYsdix2Kkg2KfZhNiq2Yog2YrZgtmI2YUg2KjZh9inINin2YTZhdio2LHZhdis2YjZhi4g2YfYsNinINiq2YXZitmK2LIg2K3Yp9iz2YUuINil2YbZh9inINiq2LnZhdmEINi52YTZiSDYpdi22YHYp9ihINin2YTYt9in2KjYuSDYp9mE2K/ZitmF2YLYsdin2LfZiiDYudmE2Ykg2KrYt9mI2YrYsSDYp9mE2KjYsdmF2KzZitin2KrYjCDZhdmF2Kcg2YrYs9mF2K0g2YTZhdiy2YrYryDZhdmGINin2YTZhtin2LMg2KjYp9mE2YXYtNin2LHZg9ipINmB2Yog2KfZhNio2YbYp9ihINmI2KfZhNin2KjYqtmD2KfYsdiMINmI2YfYsNinINi02YrYoSDYpdmK2KzYp9io2Yog2YTZhNis2YXZiti5LiDYqNin2YTZhtiz2KjYqSDZhNmE2YXYqNix2YXYrNmK2YbYjCDZh9iw2Ycg2YHYsdi12Kkg2YTZhNiq2LfZiNixINmI2KfZhNin2LHYqtmC2KfYoSDZgdmKINiz2YTYs9mE2Kkg2KfZhNmC2YrZhdip2Iwg2YjYp9mE2KrYsdmD2YrYsiDYudmE2Ykg2K3ZhCDYp9mE2YXYtNmD2YTYp9iqINin2YTYqtmKINmE2Kcg2YrZhdmD2YYg2K3ZhNmH2Kcg2KXZhNinINio2KfZhNio2LHYp9i52Kkg2KfZhNio2LTYsdmK2Kkg2YjYp9mE2YHZh9mFINin2YTYqtmC2YbZiiDYp9mE2LnZhdmK2YIuINin2YTZhdiz2KrZgtio2YQg2YTZitizINmF2LnYsdmD2Kkg2KjZitmGINin2YTZg9mI2K8g2YjYp9mE2YAgTm8tQ29kZdiMINio2YQg2YfZiCDZhdiz2KrZgtio2YQg2YXZhiDYp9mE2KrYudin2YjZhiDZiNin2YTYqtmD2KfZhdmEINio2YrZhtmH2YXYpy4g2YXYpyDYsdij2YrZgyDYo9mG2KrYnyDZh9mEINiq2KrZgdmCINmF2Lkg2YfYsNinINin2YTYqtit2YTZitmE2J8g2LTYp9ix2YPZhtinINmI2KzZh9ipINmG2LjYsdmDINmB2Yog2KfZhNiq2LnZhNmK2YLYp9iqLlwifSwgXCJ0eXBlXCI6IFwicGFyYWdyYXBoXCJ9LCB7XCJkYXRhXCI6IHtcInRleHRcIjogXCLZhdi12KfYr9ixINmI2KPYr9mI2KfYqiDZhdiw2YPZiNix2KlcIiwgXCJsZXZlbFwiOiA0fSwgXCJ0eXBlXCI6IFwiaGVhZGVyXCJ9LCB7XCJkYXRhXCI6IHtcIml0ZW1zXCI6IFtcIkJ1YmJsZS5pb1wiLCBcIldlYmZsb3cuY29tXCIsIFwiWmFwaWVyLmNvbVwiLCBcIlRoZSBGdXR1cmUgb2YgSm9icyBSZXBvcnQgKFdvcmxkIEVjb25vbWljIEZvcnVtKVwiXSwgXCJzdHlsZVwiOiBcInVub3JkZXJlZFwifSwgXCJ0eXBlXCI6IFwibGlzdFwifV19IiwiZmVhdHVyZWRfaW1hZ2VfdXJsIjpudWxsLCJzdGF0dXMiOiJwdWJsaXNoZWQiLCJwdWJsaXNoZWRfYXQiOiIyMDI1LTA3LTExVDEwOjMwOjAwKzAwOjAwIiwiY3JlYXRlZF9hdCI6IjIwMjUtMDctMTFUMTA6MzA6MDArMDA6MDAiLCJ1cGRhdGVkX2F0IjoiMjAyNS0wNy0xMVQxMDozMDowMCswMDowMCIsInRhZ3MiOlsi2YXYs9iq2YLYqNmEINin2YTYqNix2YXYrNipIiwiTm8tQ29kZSIsItmF2KjYsdmF2KzZitmGIiwi2LPZiNmCINin2YTYudmF2YQiXSwicmVhZGluZ190aW1lIjo5LCJhdXRob3IiOiLYpdiz2YXYp9i52YrZhCDYo9it2YXYryIsIm1ldGFfZGVzY3JpcHRpb24iOiLZhtmC2KfYtCDYudmF2YrZgiDYrdmI2YQg2YXYs9iq2YLYqNmEINmF2YfZhtipINin2YTYqNix2YXYrNipINmB2Yog2LjZhCDYtdi52YjYryDYo9iv2YjYp9iqIE5vLUNvZGUuINmH2YQg2YfZiiDZhtmH2KfZitipINin2YTZhdio2LHZhdis2YrZhiDYo9mFINiq2LfZiNixINi32KjZiti52Yog2YTYo9iv2YjYp9ix2YfZhdifIiwic2VvX2tleXdvcmRzIjpudWxsLCJsYW5ndWFnZSI6Im1peGVkIiwiZGlyZWN0aW9uIjoiYXV0byIsInZhbGlkYXRpb25fZXJyb3JzIjpbXSwidmFsaWRhdGlvbl93YXJuaW5ncyI6W10sImZlYXR1cmVkIjpmYWxzZSwiY2F0ZWdvcnkiOm51bGwsInNlb190aXRsZSI6bnVsbCwic2VvX2Rlc2NyaXB0aW9uIjpudWxsfQ==", "status": 200, "url": "https://zgktrwpladrkhhemhnni.supabase.co/rest/v1/articles?select=*&slug=eq.future-of-programming-will-no-code-replace-developers&status=eq.published"}, "revalidate": 604800, "tags": []}