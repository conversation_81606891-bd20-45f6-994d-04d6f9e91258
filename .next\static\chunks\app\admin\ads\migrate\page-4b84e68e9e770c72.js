(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6693],{2099:(e,s,a)=>{"use strict";a.d(s,{ND:()=>d});var l=a(5647);let r="https://zgktrwpladrkhhemhnni.supabase.co",t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04";if(!r)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!t)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let d=(0,l.UU)(r,t,{db:{schema:"public"},auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}})},5579:(e,s,a)=>{"use strict";a.r(s),a.d(s,{default:()=>n});var l=a(5155),r=a(2115),t=a(2099),d=a(6874),i=a.n(d);function n(){let[e,s]=(0,r.useState)(!1),[a,d]=(0,r.useState)(null),n=async()=>{s(!0);try{let{data:e,error:s}=await t.ND.from("ads").select("id, title, status, created_at").limit(10),{data:a,error:l}=await t.ND.from("advertisements").select("id, title, is_active, created_at").limit(10);d({success:!0,oldTable:{exists:!s,count:(null==e?void 0:e.length)||0,sample:e||[]},newTable:{exists:!l,count:(null==a?void 0:a.length)||0,sample:a||[]}})}catch(e){d({success:!1,error:e.message})}finally{s(!1)}},c=async()=>{if(confirm("هل أنت متأكد من ترحيل البيانات من الجدول القديم؟")){s(!0);try{let{error:e}=await t.ND.rpc("migrate_ads_to_advertisements");if(e){let{error:e}=await t.ND.from("advertisements").insert([])}alert("تم ترحيل البيانات بنجاح"),await n()}catch(e){alert("خطأ في الترحيل: "+e.message)}finally{s(!1)}}},x=async()=>{if(confirm("⚠️ تحذير: هل أنت متأكد من حذف الجدول القديم؟ هذا الإجراء لا يمكن التراجع عنه!")&&confirm("تأكيد نهائي: سيتم حذف جدول ads القديم نهائياً!")){s(!0);try{await t.ND.rpc("drop_old_ads_table"),alert("تم حذف الجدول القديم بنجاح"),await n()}catch(e){alert("خطأ في حذف الجدول: "+e.message)}finally{s(!1)}}};return(0,l.jsx)("div",{className:"min-h-screen bg-dark-background",children:(0,l.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,l.jsxs)("div",{className:"max-w-4xl mx-auto",children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,l.jsxs)("div",{children:[(0,l.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"ترحيل بيانات الإعلانات"}),(0,l.jsx)("p",{className:"text-dark-text-secondary",children:"ترحيل البيانات من الجدول القديم (ads) إلى الجدول الجديد (advertisements)"})]}),(0,l.jsx)(i(),{href:"/admin/ads",className:"bg-gray-600 hover:bg-gray-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors",children:"العودة للإعلانات"})]}),(0,l.jsxs)("div",{className:"bg-yellow-900 border border-yellow-700 rounded-xl p-6 mb-8",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-yellow-300 mb-4",children:"\uD83D\uDD0D المشكلة المكتشفة"}),(0,l.jsxs)("div",{className:"text-yellow-100 space-y-2",children:[(0,l.jsxs)("p",{children:["• لديك جدولين منفصلين للإعلانات: ",(0,l.jsx)("code",{children:"ads"})," (القديم) و ",(0,l.jsx)("code",{children:"advertisements"})," (الجديد)"]}),(0,l.jsx)("p",{children:"• لوحة التحكم تقرأ من الجدول الجديد بينما بعض المكونات تقرأ من القديم"}),(0,l.jsx)("p",{children:"• هذا يسبب عدم تطابق في البيانات المعروضة"}),(0,l.jsx)("p",{children:"• الرابط الذي أرسلته يشير إلى محرر SQL في Supabase حيث يمكنك رؤية هذه المشكلة"})]})]}),(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-3 gap-6 mb-8",children:[(0,l.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"فحص الجداول"}),(0,l.jsx)("p",{className:"text-gray-300 mb-4",children:"فحص حالة الجدولين القديم والجديد"}),(0,l.jsx)("button",{onClick:n,disabled:e,className:"w-full bg-blue-600 text-white px-4 py-3 rounded-lg hover:bg-blue-700 disabled:opacity-50 transition-colors",children:e?"جاري الفحص...":"فحص الجداول"})]}),(0,l.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"ترحيل البيانات"}),(0,l.jsx)("p",{className:"text-gray-300 mb-4",children:"ترحيل البيانات المتبقية من القديم للجديد"}),(0,l.jsx)("button",{onClick:c,disabled:e,className:"w-full bg-green-600 text-white px-4 py-3 rounded-lg hover:bg-green-700 disabled:opacity-50 transition-colors",children:e?"جاري الترحيل...":"ترحيل البيانات"})]}),(0,l.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"حذف الجدول القديم"}),(0,l.jsx)("p",{className:"text-gray-300 mb-4",children:"حذف الجدول القديم نهائياً (خطر!)"}),(0,l.jsx)("button",{onClick:x,disabled:e,className:"w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 disabled:opacity-50 transition-colors",children:e?"جاري الحذف...":"⚠️ حذف الجدول القديم"})]})]}),a&&(0,l.jsxs)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:a.success?"✅ نتائج الفحص":"❌ خطأ في الفحص"}),a.success?(0,l.jsxs)("div",{className:"space-y-6",children:[(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,l.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-red-400 mb-3",children:"\uD83D\uDCCA الجدول القديم (ads)"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-300",children:"الحالة:"}),(0,l.jsx)("span",{className:a.oldTable.exists?"text-green-400":"text-red-400",children:a.oldTable.exists?"موجود":"غير موجود"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-300",children:"عدد الإعلانات:"}),(0,l.jsx)("span",{className:"text-white",children:a.oldTable.count})]})]}),a.oldTable.sample.length>0&&(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:"عينة من البيانات:"}),(0,l.jsx)("div",{className:"space-y-1",children:a.oldTable.sample.slice(0,3).map(e=>(0,l.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-900 p-2 rounded",children:[e.title," - ",e.status]},e.id))})]})]}),(0,l.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-green-400 mb-3",children:"\uD83D\uDCCA الجدول الجديد (advertisements)"}),(0,l.jsxs)("div",{className:"space-y-2",children:[(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-300",children:"الحالة:"}),(0,l.jsx)("span",{className:a.newTable.exists?"text-green-400":"text-red-400",children:a.newTable.exists?"موجود":"غير موجود"})]}),(0,l.jsxs)("div",{className:"flex justify-between",children:[(0,l.jsx)("span",{className:"text-gray-300",children:"عدد الإعلانات:"}),(0,l.jsx)("span",{className:"text-white",children:a.newTable.count})]})]}),a.newTable.sample.length>0&&(0,l.jsxs)("div",{className:"mt-4",children:[(0,l.jsx)("h4",{className:"text-sm font-semibold text-gray-300 mb-2",children:"عينة من البيانات:"}),(0,l.jsx)("div",{className:"space-y-1",children:a.newTable.sample.slice(0,3).map(e=>(0,l.jsxs)("div",{className:"text-xs text-gray-400 bg-gray-900 p-2 rounded",children:[e.title," - ",e.is_active?"نشط":"غير نشط"]},e.id))})]})]})]}),(0,l.jsxs)("div",{className:"bg-blue-900 border border-blue-700 rounded-lg p-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-blue-300 mb-3",children:"\uD83D\uDCA1 التوصيات"}),(0,l.jsxs)("div",{className:"text-blue-100 space-y-2",children:[a.oldTable.exists&&a.oldTable.count>0&&(0,l.jsx)("p",{children:"• يُنصح بترحيل البيانات المتبقية من الجدول القديم"}),a.oldTable.exists&&0===a.oldTable.count&&(0,l.jsx)("p",{children:"• يمكن حذف الجدول القديم بأمان لأنه فارغ"}),(0,l.jsx)("p",{children:"• تأكد من أن جميع المكونات تستخدم الجدول الجديد (advertisements)"}),(0,l.jsx)("p",{children:"• قم بعمل نسخة احتياطية قبل حذف الجدول القديم"})]})]})]}):(0,l.jsx)("div",{className:"bg-red-900 border border-red-700 rounded-lg p-4",children:(0,l.jsx)("p",{className:"text-red-300",children:a.error})})]}),(0,l.jsxs)("div",{className:"mt-8 bg-dark-card rounded-xl p-6 border border-gray-800",children:[(0,l.jsx)("h2",{className:"text-xl font-semibold text-white mb-4",children:"معلومات الترحيل"}),(0,l.jsxs)("div",{className:"space-y-3 text-gray-300",children:[(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"الجدول القديم (ads):"})," يستخدم status, placement, description"]}),(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"الجدول الجديد (advertisements):"})," يستخدم is_active, position, content"]}),(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"الترحيل:"})," يحول البيانات من الهيكل القديم للجديد تلقائياً"]}),(0,l.jsxs)("p",{children:["• ",(0,l.jsx)("strong",{children:"الحذف:"})," يحذف الجدول القديم نهائياً بعد التأكد من الترحيل"]})]})]}),(0,l.jsx)("div",{className:"mt-8 text-center",children:(0,l.jsxs)("div",{className:"space-x-4 space-x-reverse",children:[(0,l.jsx)(i(),{href:"/admin/ads",className:"inline-block px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"إدارة الإعلانات"}),(0,l.jsx)(i(),{href:"/admin/ads/sync",className:"inline-block px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"مزامنة البيانات"}),(0,l.jsx)("a",{href:"https://supabase.com/dashboard/project/zgktrwpladrkhhemhnni/editor",target:"_blank",rel:"noopener noreferrer",className:"inline-block px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors",children:"محرر SQL"})]})})]})})})}},8668:(e,s,a)=>{Promise.resolve().then(a.bind(a,5579))}},e=>{var s=s=>e(e.s=s);e.O(0,[6874,5647,8441,1684,7358],()=>s(8668)),_N_E=e.O()}]);