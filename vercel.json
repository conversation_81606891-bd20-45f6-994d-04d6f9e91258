{"framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": "out", "installCommand": "npm install", "headers": [{"source": "/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}, {"key": "X-Content-Type-Options", "value": "nosniff"}, {"key": "X-Frame-Options", "value": "DENY"}, {"key": "Referrer-Policy", "value": "strict-origin-when-cross-origin"}]}, {"source": "/_next/static/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/static-data/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=86400"}]}], "redirects": [{"source": "/admin", "destination": "/404", "permanent": false}, {"source": "/api/(.*)", "destination": "/404", "permanent": false}]}