{"name": "technoflash", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "npm run build:static && next build", "build:static": "node scripts/build-static.js", "build:export": "npm run build && next export", "start": "next start", "lint": "next lint", "update-static": "node scripts/build-static.js"}, "dependencies": {"@supabase/ssr": "^0.1.0", "@supabase/supabase-js": "^2.38.0", "dotenv": "^17.2.0", "isomorphic-dompurify": "^2.26.0", "next": "^15.1.0", "node-fetch": "^3.3.2", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "swiper": "^11.2.10"}, "devDependencies": {"@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "autoprefixer": "^10.0.1", "critters": "^0.0.23", "eslint": "^8", "eslint-config-next": "14.0.0", "postcss": "^8", "tailwindcss": "^3.3.0", "typescript": "^5"}}