(()=>{var e={};e.id=5395,e.ids=[5395],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},19358:(e,t,s)=>{"use strict";s.d(t,{default:()=>d});var r=s(60687),i=s(43210),a=s(16391),l=s(85814),n=s.n(l),o=s(98244);function d(){let[e,t]=(0,i.useState)([]),[s,l]=(0,i.useState)(!0),[d,c]=(0,i.useState)("all"),[x,h]=(0,i.useState)("all"),[u,m]=(0,i.useState)("all"),[p,g]=(0,i.useState)(null),b=async()=>{try{l(!0);let{data:e,error:s}=await a.ND.from("advertisements").select("*").order("created_at",{ascending:!1});if(s)return void console.error("Error fetching ads:",s);t(e||[])}catch(e){console.error("Error fetching ads:",e)}finally{l(!1)}},v=async(e,t)=>{try{let s={is_paused:!t,paused_at:t?null:new Date().toISOString(),pause_reason:t?null:"تم الإيقاف يدوياً من لوحة التحكم",updated_at:new Date().toISOString()},{error:r}=await a.ND.from("advertisements").update(s).eq("id",e);if(r){console.error("Error toggling ad pause:",r),alert("خطأ في تغيير حالة الإعلان: "+r.message);return}await b(),alert(t?"تم تشغيل الإعلان":"تم إيقاف الإعلان مؤقتاً")}catch(e){console.error("Error in togglePauseAd:",e),alert("خطأ في تغيير حالة الإعلان: "+e.message)}},f=async(s,r)=>{try{let{error:i}=await a.ND.from("advertisements").update({is_active:!r,updated_at:new Date().toISOString()}).eq("id",s);if(i)return void console.error("Error updating ad:",i);t(e.map(e=>e.id===s?{...e,is_active:!r}:e))}catch(e){console.error("Error updating ad:",e)}},j=async s=>{if(confirm("هل أنت متأكد من حذف هذا الإعلان؟"))try{let{error:r}=await a.ND.from("advertisements").delete().eq("id",s);if(r)return void console.error("Error deleting ad:",r);t(e.filter(e=>e.id!==s))}catch(e){console.error("Error deleting ad:",e)}},y=e.filter(e=>("active"!==d||!!e.is_active)&&("inactive"!==d||!e.is_active)&&("all"===x||e.type===x)&&("all"===u||e.position===u)),N=e=>{switch(e){case"text":return"bg-blue-900 text-blue-300";case"image":return"bg-green-900 text-green-300";case"video":return"bg-purple-900 text-purple-300";case"html":return"bg-orange-900 text-orange-300";case"banner":return"bg-pink-900 text-pink-300";case"adsense":return"bg-yellow-900 text-yellow-300";default:return"bg-gray-900 text-gray-300"}},w=e=>({header:"الهيدر",footer:"الفوتر","sidebar-right":"الشريط الجانبي","article-body-start":"بداية المقال","article-body-mid":"وسط المقال","article-body-end":"نهاية المقال","in-content":"داخل المحتوى"})[e]||e,_=Array.from(new Set(e.map(e=>e.type))),k=Array.from(new Set(e.map(e=>e.position)));return s?(0,r.jsx)("div",{className:"min-h-screen bg-dark-background flex items-center justify-center",children:(0,r.jsx)("div",{className:"text-white text-xl",children:"جاري تحميل الإعلانات..."})}):(0,r.jsx)("div",{className:"min-h-screen bg-dark-background",children:(0,r.jsx)("div",{className:"container mx-auto px-4 py-8",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"إدارة الإعلانات"}),(0,r.jsxs)("p",{className:"text-dark-text-secondary",children:["إجمالي الإعلانات: ",e.length," | النشطة: ",e.filter(e=>e.is_active).length," | غير النشطة: ",e.filter(e=>!e.is_active).length]})]}),(0,r.jsxs)("div",{className:"flex space-x-4 space-x-reverse",children:[(0,r.jsx)(n(),{href:"/admin/ads/new",className:"bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-colors",children:"إضافة إعلان جديد"}),(0,r.jsx)(n(),{href:"/admin/ads/sync",className:"bg-green-600 hover:bg-green-700 text-white px-6 py-3 rounded-lg font-semibold transition-colors",children:"مزامنة البيانات"})]})]}),(0,r.jsx)("div",{className:"bg-dark-card rounded-xl p-6 border border-gray-800 mb-8",children:(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"الحالة:"}),(0,r.jsxs)("select",{value:d,onChange:e=>c(e.target.value),className:"w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600",children:[(0,r.jsxs)("option",{value:"all",children:["جميع الإعلانات (",e.length,")"]}),(0,r.jsxs)("option",{value:"active",children:["النشطة (",e.filter(e=>e.is_active).length,")"]}),(0,r.jsxs)("option",{value:"inactive",children:["غير النشطة (",e.filter(e=>!e.is_active).length,")"]})]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"النوع:"}),(0,r.jsxs)("select",{value:x,onChange:e=>h(e.target.value),className:"w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600",children:[(0,r.jsx)("option",{value:"all",children:"جميع الأنواع"}),_.map(e=>(0,r.jsx)("option",{value:e,children:e.toUpperCase()},e))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"الموضع:"}),(0,r.jsxs)("select",{value:u,onChange:e=>m(e.target.value),className:"w-full bg-gray-700 text-white rounded-lg px-3 py-2 border border-gray-600",children:[(0,r.jsx)("option",{value:"all",children:"جميع المواضع"}),k.map(e=>(0,r.jsx)("option",{value:e,children:w(e)},e))]})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block text-white font-medium mb-2",children:"الإجراءات:"}),(0,r.jsx)("button",{onClick:b,className:"w-full bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg transition-colors",children:"تحديث القائمة"})]})]})}),(0,r.jsxs)("div",{className:"bg-dark-card rounded-xl border border-gray-800 overflow-hidden",children:[(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-gray-800",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-right p-4 text-white font-semibold",children:"العنوان"}),(0,r.jsx)("th",{className:"text-right p-4 text-white font-semibold",children:"النوع"}),(0,r.jsx)("th",{className:"text-right p-4 text-white font-semibold",children:"الموضع"}),(0,r.jsx)("th",{className:"text-right p-4 text-white font-semibold",children:"الحالة"}),(0,r.jsx)("th",{className:"text-right p-4 text-white font-semibold",children:"الإحصائيات"}),(0,r.jsx)("th",{className:"text-right p-4 text-white font-semibold",children:"تاريخ الإنشاء"}),(0,r.jsx)("th",{className:"text-right p-4 text-white font-semibold",children:"الإجراءات"})]})}),(0,r.jsx)("tbody",{children:y.map((e,t)=>(0,r.jsxs)("tr",{className:`border-t border-gray-700 ${t%2==0?"bg-gray-900/50":""}`,children:[(0,r.jsx)("td",{className:"p-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-medium",children:e.title}),(0,r.jsx)("p",{className:"text-gray-400 text-sm mt-1 line-clamp-2",children:e.content.length>100?`${e.content.substring(0,100)}...`:e.content})]})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsx)("span",{className:`px-2 py-1 rounded text-xs font-medium ${N(e.type)}`,children:e.type.toUpperCase()})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsx)("span",{className:"text-gray-300 text-sm",children:w(e.position)})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,r.jsx)("button",{onClick:()=>f(e.id,e.is_active),className:`px-3 py-1 rounded-full text-xs font-medium transition-colors ${e.is_active?"bg-green-900 text-green-300 hover:bg-green-800":"bg-red-900 text-red-300 hover:bg-red-800"}`,children:e.is_active?"نشط":"غير نشط"}),e.is_active&&(0,r.jsx)("button",{onClick:()=>{v(e.id,e.is_paused||!1)},className:`px-3 py-1 rounded-full text-xs font-medium transition-colors ${e.is_paused?"bg-yellow-900 text-yellow-300 hover:bg-yellow-800":"bg-blue-900 text-blue-300 hover:bg-blue-800"}`,children:e.is_paused?"⏸️ مؤقف":"▶️ يعمل"}),e.is_paused&&e.pause_reason&&(0,r.jsx)("div",{className:"text-xs text-yellow-400 mt-1",children:e.pause_reason})]})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsxs)("div",{className:"text-sm text-gray-300",children:[(0,r.jsxs)("div",{children:["\uD83D\uDC41️ ",e.view_count||0]}),(0,r.jsxs)("div",{children:["\uD83D\uDC46 ",e.click_count||0]})]})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsx)("span",{className:"text-gray-400 text-sm",children:new Date(e.created_at).toLocaleDateString("ar-SA")})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex flex-wrap gap-1",children:[(0,r.jsx)("button",{onClick:()=>g(e),className:"bg-green-600 hover:bg-green-700 text-white px-2 py-1 rounded text-xs transition-colors",children:"معاينة"}),(0,r.jsx)(n(),{href:`/admin/ads/${e.id}/edit`,className:"bg-blue-600 hover:bg-blue-700 text-white px-2 py-1 rounded text-xs transition-colors",children:"تعديل"}),e.is_active&&(0,r.jsx)("button",{onClick:()=>{v(e.id,e.is_paused||!1)},className:`px-2 py-1 rounded text-xs transition-colors ${e.is_paused?"bg-yellow-600 hover:bg-yellow-700 text-white":"bg-orange-600 hover:bg-orange-700 text-white"}`,children:e.is_paused?"تشغيل":"إيقاف"}),(0,r.jsx)("button",{onClick:()=>j(e.id),className:"bg-red-600 hover:bg-red-700 text-white px-2 py-1 rounded text-xs transition-colors",children:"حذف"})]})})]},e.id))})]})}),0===y.length&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"text-gray-400 text-lg mb-4",children:"لا توجد إعلانات تطابق الفلاتر المحددة"}),(0,r.jsx)(n(),{href:"/admin/ads/new",className:"bg-primary hover:bg-primary/90 text-white px-6 py-3 rounded-lg font-semibold transition-colors inline-block",children:"إضافة إعلان جديد"})]})]}),(0,r.jsxs)("div",{className:"mt-8 flex justify-center space-x-4 space-x-reverse",children:[(0,r.jsx)(n(),{href:"/test-ads-comprehensive",className:"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors",children:"اختبار الإعلانات"}),(0,r.jsx)(n(),{href:"/test-ads-integration",className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"اختبار التكامل"}),(0,r.jsx)(n(),{href:"/test-dashboard",className:"px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors",children:"لوحة الاختبار"})]}),p&&(0,r.jsx)("div",{className:"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4",children:(0,r.jsx)("div",{className:"bg-dark-card rounded-xl border border-gray-800 max-w-2xl w-full max-h-[80vh] overflow-y-auto",children:(0,r.jsxs)("div",{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,r.jsx)("h3",{className:"text-xl font-bold text-white",children:"معاينة الإعلان"}),(0,r.jsx)("button",{onClick:()=>g(null),className:"text-gray-400 hover:text-white text-2xl",children:"\xd7"})]}),(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsx)("h4",{className:"text-lg font-semibold text-white mb-2",children:p.title}),(0,r.jsxs)("div",{className:"flex space-x-4 space-x-reverse text-sm text-gray-400 mb-4",children:[(0,r.jsxs)("span",{children:["النوع: ",p.type]}),(0,r.jsxs)("span",{children:["الموضع: ",w(p.position)]}),(0,r.jsxs)("span",{children:["الحالة: ",p.is_active?"نشط":"غير نشط"]})]})]}),(0,r.jsx)("div",{className:"bg-gray-900 rounded-lg p-4",children:(0,r.jsx)(o.A,{ad:p,className:"w-full"})}),(0,r.jsxs)("div",{className:"mt-4 flex justify-end space-x-2 space-x-reverse",children:[(0,r.jsx)("button",{onClick:()=>g(null),className:"px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors",children:"إغلاق"}),(0,r.jsx)(n(),{href:`/admin/ads/${p.id}/edit`,className:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"تعديل الإعلان"})]})]})})})]})})})}},20769:(e,t,s)=>{"use strict";s.d(t,{ProtectedRoute:()=>l});var r=s(60687),i=s(63213),a=s(16189);function l({children:e}){let{user:t,loading:s}=(0,i.A)();return((0,a.useRouter)(),s)?(0,r.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"}),(0,r.jsx)("p",{className:"text-dark-text-secondary",children:"جاري التحقق من صلاحيات الوصول..."})]})}):t?(0,r.jsx)(r.Fragment,{children:e}):null}s(43210)},24612:(e,t,s)=>{"use strict";s.d(t,{default:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\admin\\\\ads\\\\simple-page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\app\\admin\\ads\\simple-page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33134:(e,t,s)=>{Promise.resolve().then(s.bind(s,67083))},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},56619:(e,t,s)=>{Promise.resolve().then(s.bind(s,19358))},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,t,s)=>{"use strict";s.d(t,{ProtectedRoute:()=>r});let r=(0,s(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},72669:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>r.default});var r=s(24612)},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},95509:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>l.a,__next_app__:()=>x,pages:()=>c,routeModule:()=>h,tree:()=>d});var r=s(65239),i=s(48088),a=s(88170),l=s.n(a),n=s(30893),o={};for(let e in n)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);s.d(t,o);let d={children:["",{children:["admin",{children:["ads",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,72669)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\ads\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(s.bind(s,99111)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,94431)),"C:\\Users\\<USER>\\4\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(s.bind(s,67393)),"C:\\Users\\<USER>\\4\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(s.bind(s,54413)),"C:\\Users\\<USER>\\4\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(s.t.bind(s,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(s.t.bind(s,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,c=["C:\\Users\\<USER>\\4\\src\\app\\admin\\ads\\page.tsx"],x={require:s,loadChunk:()=>Promise.resolve()},h=new r.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/ads/page",pathname:"/admin/ads",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},96182:(e,t,s)=>{Promise.resolve().then(s.bind(s,20769))},96371:(e,t,s)=>{Promise.resolve().then(s.bind(s,24612))},99111:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(37413),i=s(67083);function a({children:e}){return(0,r.jsx)(i.ProtectedRoute,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[2714,474,6656,8244],()=>s(95509));module.exports=r})();