(()=>{var e={};e.id=6255,e.ids=[6255],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13369:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(60687),i=r(43210),a=r(85814),n=r.n(a);function d(){let[e,t]=(0,i.useState)([]),[r,a]=(0,i.useState)(!0),[d,l]=(0,i.useState)(null),c=async()=>{try{a(!0);let e=await fetch("/api/services");if(!e.ok)throw Error("فشل في جلب الخدمات");let r=await e.json();t(r.services||[])}catch(e){console.error("Error fetching services:",e),l("حدث خطأ في جلب الخدمات")}finally{a(!1)}},o=async e=>{if(confirm("هل أنت متأكد من حذف هذه الخدمة؟"))try{if(!(await fetch(`/api/services/${e}`,{method:"DELETE"})).ok)throw Error("فشل في حذف الخدمة");c()}catch(e){console.error("Error deleting service:",e),alert("حدث خطأ في حذف الخدمة")}},x=e=>(0,s.jsx)("span",{className:`px-2 py-1 rounded-full text-xs font-medium ${{active:"bg-green-100 text-green-800",inactive:"bg-red-100 text-red-800",draft:"bg-yellow-100 text-yellow-800"}[e]||"bg-gray-100 text-gray-800"}`,children:{active:"نشط",inactive:"غير نشط",draft:"مسودة"}[e]||e}),m=e=>{switch(e.pricing_type){case"free":return"مجاني";case"paid":return e.pricing_amount?`${e.pricing_amount} ${e.pricing_currency}`:"مدفوع";default:return"حسب الطلب"}};return r?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-gray-600",children:"جاري تحميل الخدمات..."})]})}):d?(0,s.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("p",{className:"text-red-600 mb-4",children:d}),(0,s.jsx)("button",{onClick:c,className:"bg-primary text-white px-4 py-2 rounded-lg hover:bg-primary/90",children:"إعادة المحاولة"})]})}):(0,s.jsx)("div",{className:"min-h-screen bg-gray-50",children:(0,s.jsxs)("div",{className:"container mx-auto px-4 py-8",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"إدارة الخدمات"}),(0,s.jsx)("p",{className:"text-gray-600 mt-2",children:"إدارة وتحرير جميع الخدمات المتاحة في الموقع"})]}),(0,s.jsx)(n(),{href:"/admin/services/new",className:"bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200",children:"إضافة خدمة جديدة"})]}),(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8",children:[(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-primary mb-2",children:e.length}),(0,s.jsx)("div",{className:"text-gray-600",children:"إجمالي الخدمات"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-green-600 mb-2",children:e.filter(e=>"active"===e.status).length}),(0,s.jsx)("div",{className:"text-gray-600",children:"خدمات نشطة"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-yellow-600 mb-2",children:e.filter(e=>e.featured).length}),(0,s.jsx)("div",{className:"text-gray-600",children:"خدمات مميزة"})]}),(0,s.jsxs)("div",{className:"bg-white rounded-lg p-6 shadow-sm",children:[(0,s.jsx)("div",{className:"text-2xl font-bold text-blue-600 mb-2",children:e.filter(e=>"free"===e.pricing_type).length}),(0,s.jsx)("div",{className:"text-gray-600",children:"خدمات مجانية"})]})]}),0===e.length?(0,s.jsxs)("div",{className:"bg-white rounded-lg shadow-sm p-12 text-center",children:[(0,s.jsx)("div",{className:"text-gray-400 mb-4",children:(0,s.jsx)("svg",{className:"w-16 h-16 mx-auto",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,s.jsx)("h3",{className:"text-xl font-medium text-gray-900 mb-2",children:"لا توجد خدمات"}),(0,s.jsx)("p",{className:"text-gray-600 mb-6",children:"ابدأ بإضافة خدمة جديدة لعرضها في الموقع"}),(0,s.jsx)(n(),{href:"/admin/services/new",className:"inline-block bg-primary text-white px-6 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200",children:"إضافة خدمة جديدة"})]}):(0,s.jsx)("div",{className:"bg-white rounded-lg shadow-sm overflow-hidden",children:(0,s.jsx)("div",{className:"overflow-x-auto",children:(0,s.jsxs)("table",{className:"min-w-full divide-y divide-gray-200",children:[(0,s.jsx)("thead",{className:"bg-gray-50",children:(0,s.jsxs)("tr",{children:[(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الخدمة"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"التصنيف"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"السعر"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الحالة"}),(0,s.jsx)("th",{className:"px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider",children:"الإجراءات"})]})}),(0,s.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:e.map(e=>(0,s.jsxs)("tr",{className:"hover:bg-gray-50",children:[(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:(0,s.jsx)("div",{className:"flex items-center",children:(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"text-sm font-medium text-gray-900 flex items-center",children:[e.name,e.featured&&(0,s.jsx)("span",{className:"mr-2 bg-yellow-100 text-yellow-800 px-2 py-1 rounded-full text-xs",children:"مميز"})]}),(0,s.jsx)("div",{className:"text-sm text-gray-500",children:e.short_description||e.description.substring(0,50)+"..."})]})})}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:e.category}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm text-gray-900",children:m(e)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap",children:x(e.status)}),(0,s.jsx)("td",{className:"px-6 py-4 whitespace-nowrap text-sm font-medium",children:(0,s.jsxs)("div",{className:"flex space-x-2 space-x-reverse",children:[(0,s.jsx)(n(),{href:`/services/${e.id}`,className:"text-blue-600 hover:text-blue-900",target:"_blank",children:"عرض"}),(0,s.jsx)(n(),{href:`/admin/services/${e.id}/edit`,className:"text-indigo-600 hover:text-indigo-900",children:"تعديل"}),(0,s.jsx)("button",{onClick:()=>o(e.id),className:"text-red-600 hover:text-red-900",children:"حذف"})]})})]},e.id))})]})})})]})})}},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},20769:(e,t,r)=>{"use strict";r.d(t,{ProtectedRoute:()=>n});var s=r(60687),i=r(63213),a=r(16189);function n({children:e}){let{user:t,loading:r}=(0,i.A)();return((0,a.useRouter)(),r)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"جاري التحقق من صلاحيات الوصول..."})]})}):t?(0,s.jsx)(s.Fragment,{children:e}):null}r(43210)},26379:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\admin\\\\services\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\app\\admin\\services\\page.tsx","default")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33134:(e,t,r)=>{Promise.resolve().then(r.bind(r,67083))},33873:e=>{"use strict";e.exports=require("path")},33971:(e,t,r)=>{Promise.resolve().then(r.bind(r,13369))},34631:e=>{"use strict";e.exports=require("tls")},43753:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>x,pages:()=>o,routeModule:()=>m,tree:()=>c});var s=r(65239),i=r(48088),a=r(88170),n=r.n(a),d=r(30893),l={};for(let e in d)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>d[e]);r.d(t,l);let c={children:["",{children:["admin",{children:["services",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,26379)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\services\\page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,99111)),"C:\\Users\\<USER>\\4\\src\\app\\admin\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\4\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\4\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\4\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,o=["C:\\Users\\<USER>\\4\\src\\app\\admin\\services\\page.tsx"],x={require:r,loadChunk:()=>Promise.resolve()},m=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/admin/services/page",pathname:"/admin/services",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},67083:(e,t,r)=>{"use strict";r.d(t,{ProtectedRoute:()=>s});let s=(0,r(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},80827:(e,t,r)=>{Promise.resolve().then(r.bind(r,26379))},81630:e=>{"use strict";e.exports=require("http")},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},96182:(e,t,r)=>{Promise.resolve().then(r.bind(r,20769))},99111:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(37413),i=r(67083);function a({children:e}){return(0,s.jsx)(i.ProtectedRoute,{children:e})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[2714,6656],()=>r(43753));module.exports=s})();