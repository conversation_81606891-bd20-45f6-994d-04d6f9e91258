(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5596],{2099:(e,t,n)=>{"use strict";n.d(t,{ND:()=>a});var r=n(5647);let s="https://zgktrwpladrkhhemhnni.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04";if(!s)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!i)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let a=(0,r.UU)(s,i,{db:{schema:"public"},auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}})},2269:(e,t,n)=>{"use strict";var r=n(9509);n(8375);var s=n(2115),i=function(e){return e&&"object"==typeof e&&"default"in e?e:{default:e}}(s),a=void 0!==r&&r.env&&!0,o=function(e){return"[object String]"===Object.prototype.toString.call(e)},l=function(){function e(e){var t=void 0===e?{}:e,n=t.name,r=void 0===n?"stylesheet":n,s=t.optimizeForSpeed,i=void 0===s?a:s;c(o(r),"`name` must be a string"),this._name=r,this._deletedRulePlaceholder="#"+r+"-deleted-rule____{}",c("boolean"==typeof i,"`optimizeForSpeed` must be a boolean"),this._optimizeForSpeed=i,this._serverSheet=void 0,this._tags=[],this._injected=!1,this._rulesCount=0;var l="undefined"!=typeof window&&document.querySelector('meta[property="csp-nonce"]');this._nonce=l?l.getAttribute("content"):null}var t,n=e.prototype;return n.setOptimizeForSpeed=function(e){c("boolean"==typeof e,"`setOptimizeForSpeed` accepts a boolean"),c(0===this._rulesCount,"optimizeForSpeed cannot be when rules have already been inserted"),this.flush(),this._optimizeForSpeed=e,this.inject()},n.isOptimizeForSpeed=function(){return this._optimizeForSpeed},n.inject=function(){var e=this;if(c(!this._injected,"sheet already injected"),this._injected=!0,"undefined"!=typeof window&&this._optimizeForSpeed){this._tags[0]=this.makeStyleTag(this._name),this._optimizeForSpeed="insertRule"in this.getSheet(),this._optimizeForSpeed||(this.flush(),this._injected=!0);return}this._serverSheet={cssRules:[],insertRule:function(t,n){return"number"==typeof n?e._serverSheet.cssRules[n]={cssText:t}:e._serverSheet.cssRules.push({cssText:t}),n},deleteRule:function(t){e._serverSheet.cssRules[t]=null}}},n.getSheetForTag=function(e){if(e.sheet)return e.sheet;for(var t=0;t<document.styleSheets.length;t++)if(document.styleSheets[t].ownerNode===e)return document.styleSheets[t]},n.getSheet=function(){return this.getSheetForTag(this._tags[this._tags.length-1])},n.insertRule=function(e,t){if(c(o(e),"`insertRule` accepts only strings"),"undefined"==typeof window)return"number"!=typeof t&&(t=this._serverSheet.cssRules.length),this._serverSheet.insertRule(e,t),this._rulesCount++;if(this._optimizeForSpeed){var n=this.getSheet();"number"!=typeof t&&(t=n.cssRules.length);try{n.insertRule(e,t)}catch(e){return -1}}else{var r=this._tags[t];this._tags.push(this.makeStyleTag(this._name,e,r))}return this._rulesCount++},n.replaceRule=function(e,t){if(this._optimizeForSpeed||"undefined"==typeof window){var n="undefined"!=typeof window?this.getSheet():this._serverSheet;if(t.trim()||(t=this._deletedRulePlaceholder),!n.cssRules[e])return e;n.deleteRule(e);try{n.insertRule(t,e)}catch(t){n.insertRule(this._deletedRulePlaceholder,e)}}else{var r=this._tags[e];c(r,"old rule at index `"+e+"` not found"),r.textContent=t}return e},n.deleteRule=function(e){if("undefined"==typeof window)return void this._serverSheet.deleteRule(e);if(this._optimizeForSpeed)this.replaceRule(e,"");else{var t=this._tags[e];c(t,"rule at index `"+e+"` not found"),t.parentNode.removeChild(t),this._tags[e]=null}},n.flush=function(){this._injected=!1,this._rulesCount=0,"undefined"!=typeof window?(this._tags.forEach(function(e){return e&&e.parentNode.removeChild(e)}),this._tags=[]):this._serverSheet.cssRules=[]},n.cssRules=function(){var e=this;return"undefined"==typeof window?this._serverSheet.cssRules:this._tags.reduce(function(t,n){return n?t=t.concat(Array.prototype.map.call(e.getSheetForTag(n).cssRules,function(t){return t.cssText===e._deletedRulePlaceholder?null:t})):t.push(null),t},[])},n.makeStyleTag=function(e,t,n){t&&c(o(t),"makeStyleTag accepts only strings as second parameter");var r=document.createElement("style");this._nonce&&r.setAttribute("nonce",this._nonce),r.type="text/css",r.setAttribute("data-"+e,""),t&&r.appendChild(document.createTextNode(t));var s=document.head||document.getElementsByTagName("head")[0];return n?s.insertBefore(r,n):s.appendChild(r),r},t=[{key:"length",get:function(){return this._rulesCount}}],function(e,t){for(var n=0;n<t.length;n++){var r=t[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(e,r.key,r)}}(e.prototype,t),e}();function c(e,t){if(!e)throw Error("StyleSheet: "+t+".")}var d=function(e){for(var t=5381,n=e.length;n;)t=33*t^e.charCodeAt(--n);return t>>>0},u={};function h(e,t){if(!t)return"jsx-"+e;var n=String(t),r=e+n;return u[r]||(u[r]="jsx-"+d(e+"-"+n)),u[r]}function m(e,t){"undefined"==typeof window&&(t=t.replace(/\/style/gi,"\\/style"));var n=e+t;return u[n]||(u[n]=t.replace(/__jsx-style-dynamic-selector/g,e)),u[n]}var p=function(){function e(e){var t=void 0===e?{}:e,n=t.styleSheet,r=void 0===n?null:n,s=t.optimizeForSpeed,i=void 0!==s&&s;this._sheet=r||new l({name:"styled-jsx",optimizeForSpeed:i}),this._sheet.inject(),r&&"boolean"==typeof i&&(this._sheet.setOptimizeForSpeed(i),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),this._fromServer=void 0,this._indices={},this._instancesCounts={}}var t=e.prototype;return t.add=function(e){var t=this;void 0===this._optimizeForSpeed&&(this._optimizeForSpeed=Array.isArray(e.children),this._sheet.setOptimizeForSpeed(this._optimizeForSpeed),this._optimizeForSpeed=this._sheet.isOptimizeForSpeed()),"undefined"==typeof window||this._fromServer||(this._fromServer=this.selectFromServer(),this._instancesCounts=Object.keys(this._fromServer).reduce(function(e,t){return e[t]=0,e},{}));var n=this.getIdAndRules(e),r=n.styleId,s=n.rules;if(r in this._instancesCounts){this._instancesCounts[r]+=1;return}var i=s.map(function(e){return t._sheet.insertRule(e)}).filter(function(e){return -1!==e});this._indices[r]=i,this._instancesCounts[r]=1},t.remove=function(e){var t=this,n=this.getIdAndRules(e).styleId;if(function(e,t){if(!e)throw Error("StyleSheetRegistry: "+t+".")}(n in this._instancesCounts,"styleId: `"+n+"` not found"),this._instancesCounts[n]-=1,this._instancesCounts[n]<1){var r=this._fromServer&&this._fromServer[n];r?(r.parentNode.removeChild(r),delete this._fromServer[n]):(this._indices[n].forEach(function(e){return t._sheet.deleteRule(e)}),delete this._indices[n]),delete this._instancesCounts[n]}},t.update=function(e,t){this.add(t),this.remove(e)},t.flush=function(){this._sheet.flush(),this._sheet.inject(),this._fromServer=void 0,this._indices={},this._instancesCounts={}},t.cssRules=function(){var e=this,t=this._fromServer?Object.keys(this._fromServer).map(function(t){return[t,e._fromServer[t]]}):[],n=this._sheet.cssRules();return t.concat(Object.keys(this._indices).map(function(t){return[t,e._indices[t].map(function(e){return n[e].cssText}).join(e._optimizeForSpeed?"":"\n")]}).filter(function(e){return!!e[1]}))},t.styles=function(e){var t,n;return t=this.cssRules(),void 0===(n=e)&&(n={}),t.map(function(e){var t=e[0],r=e[1];return i.default.createElement("style",{id:"__"+t,key:"__"+t,nonce:n.nonce?n.nonce:void 0,dangerouslySetInnerHTML:{__html:r}})})},t.getIdAndRules=function(e){var t=e.children,n=e.dynamic,r=e.id;if(n){var s=h(r,n);return{styleId:s,rules:Array.isArray(t)?t.map(function(e){return m(s,e)}):[m(s,t)]}}return{styleId:h(r),rules:Array.isArray(t)?t:[t]}},t.selectFromServer=function(){return Array.prototype.slice.call(document.querySelectorAll('[id^="__jsx-"]')).reduce(function(e,t){return e[t.id.slice(2)]=t,e},{})},e}(),f=s.createContext(null);f.displayName="StyleSheetContext";var x=i.default.useInsertionEffect||i.default.useLayoutEffect,y="undefined"!=typeof window?new p:void 0;function v(e){var t=y||s.useContext(f);return t&&("undefined"==typeof window?t.add(e):x(function(){return t.add(e),function(){t.remove(e)}},[e.id,String(e.dynamic)])),null}v.dynamic=function(e){return e.map(function(e){return h(e[0],e[1])}).join(" ")},t.style=v},5596:(e,t,n)=>{"use strict";n.d(t,{A:()=>m});var r=n(5155),s=n(2115),i=n(2099),a=n(7830);function o(e){let{adSlot:t,adFormat:n="auto",adLayout:i,adLayoutKey:o,className:l="",style:c={},responsive:d=!0,fullWidthResponsive:u=!0}=e,h=(0,s.useRef)(null),m=(0,s.useRef)(!1);(0,s.useEffect)(()=>{if(m.current)return;let e=()=>{try{window.adsbygoogle?h.current&&h.current.querySelector("ins")&&((window.adsbygoogle=window.adsbygoogle||[]).push({}),m.current=!0):setTimeout(e,500)}catch(e){}},t=setTimeout(e,300);return()=>{clearTimeout(t)}},[]);let p={display:"block",textAlign:"center",minHeight:"50px",backgroundColor:"transparent",...c};return(0,r.jsx)(a.vl,{fallback:(0,r.jsx)("div",{className:"bg-gray-800 rounded-lg animate-pulse ".concat(l),style:{minHeight:"100px"},children:(0,r.jsx)("div",{className:"flex items-center justify-center h-full text-gray-400 text-sm",children:"جاري تحميل الإعلان..."})}),children:(0,r.jsx)("div",{ref:h,className:"adsense-container ".concat(l),style:{overflow:"hidden",borderRadius:"8px",...c},children:(0,r.jsx)("ins",{className:"adsbygoogle",style:p,"data-ad-client":"ca-pub-your_actual_publisher_id_here","data-ad-slot":t,"data-ad-format":n,"data-ad-layout":i,"data-ad-layout-key":o,"data-full-width-responsive":u?"true":"false"})})})}var l=n(9137),c=n.n(l),d=n(6766);function u(e){let{id:t,title:n,description:i,imageUrl:o,linkUrl:l,htmlContent:u,cssStyles:h,jsCode:m,type:p,size:f="medium",position:x="inline",animation:y="none",className:v="",targetBlank:g=!0,showCloseButton:_=!1,onClose:b,onClick:j}=e,[S,w]=(0,s.useState)(!0),[N,C]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(C(!0),m&&1)try{let e=document.createElement("script");return e.textContent=m,e.id="custom-ad-script-".concat(t),document.head.appendChild(e),()=>{let e=document.getElementById("custom-ad-script-".concat(t));e&&document.head.removeChild(e)}}catch(e){}},[m,t]);let k=()=>{w(!1),null==b||b()},R=()=>{null==j||j(),l&&(g?window.open(l,"_blank","noopener,noreferrer"):window.location.href=l)};if(!S)return null;let E="\n    custom-ad relative overflow-hidden rounded-lg shadow-lg\n    ".concat({small:"max-w-sm",medium:"max-w-md",large:"max-w-lg",full:"w-full"}[f],"\n    ").concat({top:"mb-6",bottom:"mt-6",sidebar:"mb-4",inline:"my-4"}[x],"\n    ").concat({none:"",fade:"animate-fade-in",slide:"animate-slide-in",bounce:"animate-bounce-in",pulse:"animate-pulse",rotate:"animate-spin-slow"}[y],"\n    ").concat(v,"\n  ");return"html"===p&&u?(0,r.jsx)(a.vl,{children:(0,r.jsxs)("div",{className:E,children:[h&&(0,r.jsx)(c(),{id:h.__hash,children:h}),_&&(0,r.jsx)("button",{onClick:k,className:"absolute top-2 right-2 z-10 w-6 h-6 bg-gray-800 text-white rounded-full flex items-center justify-center text-xs hover:bg-gray-700 transition-colors","aria-label":"إغلاق الإعلان",children:"\xd7"}),(0,r.jsx)("div",{dangerouslySetInnerHTML:{__html:u},onClick:R,className:"cursor-pointer"})]})}):"banner"===p?(0,r.jsx)(a.vl,{children:(0,r.jsxs)("div",{className:"".concat(E," bg-gradient-to-r from-primary to-blue-600 text-white"),children:[_&&(0,r.jsx)("button",{onClick:k,className:"absolute top-2 right-2 z-10 w-6 h-6 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center text-xs hover:bg-opacity-70 transition-colors",children:"\xd7"}),(0,r.jsxs)("div",{className:"p-4 cursor-pointer flex items-center space-x-4 space-x-reverse",onClick:R,children:[o&&(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)(d.default,{src:o,alt:n||"إعلان",width:60,height:60,className:"rounded-lg object-cover"})}),(0,r.jsxs)("div",{className:"flex-1",children:[n&&(0,r.jsx)("h3",{className:"font-bold text-lg mb-1",children:n}),i&&(0,r.jsx)("p",{className:"text-sm opacity-90",children:i})]}),(0,r.jsx)("div",{className:"flex-shrink-0",children:(0,r.jsx)("span",{className:"text-xs bg-white bg-opacity-20 px-2 py-1 rounded",children:"إعلان"})})]})]})}):"card"===p?(0,r.jsx)(a.vl,{children:(0,r.jsxs)("div",{className:"".concat(E," bg-dark-card border border-gray-700"),children:[_&&(0,r.jsx)("button",{onClick:k,className:"absolute top-2 right-2 z-10 w-6 h-6 bg-gray-800 text-white rounded-full flex items-center justify-center text-xs hover:bg-gray-700 transition-colors",children:"\xd7"}),(0,r.jsxs)("div",{className:"cursor-pointer",onClick:R,children:[o&&(0,r.jsx)("div",{className:"relative h-48 w-full",children:(0,r.jsx)(d.default,{src:o,alt:n||"إعلان",fill:!0,sizes:"(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw",className:"object-cover"})}),(0,r.jsxs)("div",{className:"p-4",children:[n&&(0,r.jsx)("h3",{className:"font-bold text-white text-lg mb-2",children:n}),i&&(0,r.jsx)("p",{className:"text-gray-300 text-sm mb-3",children:i}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded",children:"إعلان"}),(0,r.jsx)("span",{className:"text-primary text-sm font-medium",children:"اعرف المزيد ←"})]})]})]})]})}):"animated"===p?(0,r.jsx)(a.vl,{children:(0,r.jsxs)("div",{className:"".concat(E," bg-gradient-to-br from-purple-600 to-pink-600 text-white overflow-hidden"),children:[_&&(0,r.jsx)("button",{onClick:k,className:"absolute top-2 right-2 z-10 w-6 h-6 bg-black bg-opacity-50 text-white rounded-full flex items-center justify-center text-xs hover:bg-opacity-70 transition-colors",children:"\xd7"}),(0,r.jsx)("div",{className:"absolute inset-0 opacity-20",children:(0,r.jsx)("div",{className:"absolute top-0 left-0 w-full h-full bg-gradient-to-r from-transparent via-white to-transparent transform -skew-x-12 animate-shimmer"})}),(0,r.jsx)("div",{className:"relative p-4 cursor-pointer",onClick:R,children:(0,r.jsxs)("div",{className:"flex items-center space-x-4 space-x-reverse",children:[o&&(0,r.jsx)("div",{className:"flex-shrink-0 animate-pulse",children:(0,r.jsx)(d.default,{src:o,alt:n||"إعلان",width:50,height:50,className:"rounded-full object-cover"})}),(0,r.jsxs)("div",{className:"flex-1",children:[n&&(0,r.jsx)("h3",{className:"font-bold text-lg mb-1 animate-bounce",children:n}),i&&(0,r.jsx)("p",{className:"text-sm opacity-90",children:i})]})]})})]})}):null}function h(e){let{id:t,htmlContent:n,cssStyles:i,jsCode:a,className:o="",onClick:l,linkUrl:c,targetBlank:d=!0}=e,u=(0,s.useRef)(null),[h,m]=(0,s.useState)(!1),[p,f]=(0,s.useState)(!1);(0,s.useEffect)(()=>{if(u.current&&n){try{u.current.innerHTML="";let e=document.createElement("div");if(e.className="safe-html-ad safe-html-ad-".concat(t),e.innerHTML=n,i){let e=document.createElement("style");e.textContent=x(i,t),document.head.appendChild(e)}u.current.appendChild(e),a&&y(a,t),c&&(e.style.cursor="pointer",e.addEventListener("click",v)),m(!0)}catch(e){console.error("Error rendering HTML ad:",e),f(!0)}return()=>{if(document.querySelectorAll('style[data-ad-id="'.concat(t,'"]')).forEach(e=>e.remove()),u.current){let e=u.current.querySelector(".safe-html-ad");e&&e.removeEventListener("click",v)}}}},[n,i,a,t,c]);let x=(e,t)=>e.replace(/expression\s*\(/gi,"").replace(/javascript:/gi,"").replace(/@import/gi,""),y=(e,t)=>{try{let n="\n        (function() {\n          const adContainer = document.querySelector('.safe-html-ad-".concat(t,"');\n          if (!adContainer) return;\n          \n          // تقييد الوصول للكائنات الخطيرة\n          const window = undefined;\n          const document = {\n            querySelector: (selector) => adContainer.querySelector(selector),\n            querySelectorAll: (selector) => adContainer.querySelectorAll(selector),\n            getElementById: (id) => adContainer.querySelector('#' + id),\n            addEventListener: (event, handler) => adContainer.addEventListener(event, handler)\n          };\n          \n          ").concat(e,"\n        })();\n      ");setTimeout(()=>{try{Function(n)()}catch(e){}},100)}catch(e){}},v=e=>{e.preventDefault(),l&&l(),c&&(d?window.open(c,"_blank","noopener,noreferrer"):window.location.href=c)};return p?(0,r.jsx)("div",{className:"bg-gray-800 rounded-lg p-4 ".concat(o),children:(0,r.jsxs)("div",{className:"text-center text-gray-400 text-sm",children:[(0,r.jsx)("div",{className:"mb-2",children:"⚠️"}),(0,r.jsx)("div",{children:"خطأ في تحميل الإعلان"})]})}):(0,r.jsxs)("div",{className:"safe-html-ad-wrapper ".concat(o),children:[(0,r.jsx)("div",{ref:u,className:"safe-html-ad-container",style:{position:"relative",overflow:"hidden",borderRadius:"8px"},"data-ad-id":t}),h&&(0,r.jsx)("div",{className:"text-xs text-gray-500 text-center mt-2",children:(0,r.jsx)("span",{className:"bg-gray-800 px-2 py-1 rounded",children:"إعلان"})})]})}function m(e){let{ad:t,className:n=""}=e;(0,s.useEffect)(()=>{(async()=>{try{let{error:e}=await i.ND.from("advertisements").update({view_count:(t.view_count||0)+1,updated_at:new Date().toISOString()}).eq("id",t.id);e&&console.error("Error recording impression:",e)}catch(e){console.error("Error recording impression:",e)}})()},[t.id,t.view_count]);let a=async()=>{try{let{error:e}=await i.ND.from("advertisements").update({click_count:(t.click_count||0)+1,updated_at:new Date().toISOString()}).eq("id",t.id);e&&console.error("Error recording click:",e),t.target_url&&window.open(t.target_url,"_blank","noopener,noreferrer")}catch(e){console.error("Error recording click:",e)}};return"adsense"===t.type&&t.content?(0,r.jsx)(o,{adSlot:t.content,className:n,responsive:!0}):"html"===t.type&&t.content?(0,r.jsx)(h,{id:t.id,htmlContent:t.content,cssStyles:t.custom_css,jsCode:t.custom_js,className:n,onClick:a,linkUrl:t.target_url,targetBlank:!0}):"image"===t.type&&t.image_url?(0,r.jsx)(u,{id:t.id,type:"card",title:t.title,description:t.content,imageUrl:t.image_url,linkUrl:t.target_url,className:n,onClick:a,targetBlank:!0,animation:"fade"}):"banner"===t.type?(0,r.jsx)(u,{id:t.id,type:"banner",title:t.title,description:t.content,imageUrl:t.image_url,linkUrl:t.target_url,className:n,onClick:a,targetBlank:!0,animation:"slide"}):"text"===t.type?(0,r.jsx)("div",{className:"bg-dark-card rounded-xl p-4 border border-gray-700 ".concat(n),children:(0,r.jsxs)("div",{className:"cursor-pointer",onClick:a,children:[(0,r.jsx)("h3",{className:"font-bold text-white text-lg mb-2",children:t.title}),(0,r.jsx)("p",{className:"text-gray-300 text-sm mb-3",children:t.content}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded",children:"إعلان"}),t.target_url&&(0,r.jsx)("span",{className:"text-primary text-sm font-medium",children:"اعرف المزيد ←"})]})]})}):"video"===t.type&&t.video_url?(0,r.jsx)("div",{className:"bg-dark-card rounded-xl overflow-hidden border border-gray-700 ".concat(n),children:(0,r.jsxs)("div",{className:"cursor-pointer",onClick:a,children:[(0,r.jsx)("div",{className:"relative",children:(0,r.jsx)("video",{src:t.video_url,poster:t.image_url,controls:!0,className:"w-full h-48 object-cover"})}),(0,r.jsxs)("div",{className:"p-4",children:[(0,r.jsx)("h3",{className:"font-bold text-white text-lg mb-2",children:t.title}),(0,r.jsx)("p",{className:"text-gray-300 text-sm mb-3",children:t.content}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsx)("span",{className:"text-xs text-gray-500 bg-gray-800 px-2 py-1 rounded",children:"إعلان فيديو"}),t.target_url&&(0,r.jsx)("span",{className:"text-primary text-sm font-medium",children:"مشاهدة المزيد ←"})]})]})]})}):(0,r.jsx)("div",{className:"bg-gray-800 rounded-xl p-4 border border-gray-700 ".concat(n),children:(0,r.jsxs)("div",{className:"text-center text-gray-400",children:[(0,r.jsx)("div",{className:"mb-2",children:"⚠️"}),(0,r.jsxs)("div",{className:"text-sm",children:["نوع إعلان غير مدعوم: ",t.type]})]})})}},7830:(e,t,n)=>{"use strict";n.d(t,{DevHydrationSuppressor:()=>i,vl:()=>a});var r=n(5155),s=n(2115);function i(){return(0,s.useEffect)(()=>{},[]),null}function a(e){let{children:t,fallback:n=(0,r.jsx)("div",{className:"animate-pulse bg-gray-800 rounded h-4 w-32"})}=e;return!function(){let[e,t]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{t(!0)},[]),e}()?(0,r.jsx)(r.Fragment,{children:n}):(0,r.jsx)(r.Fragment,{children:t})}},8375:()=>{},9137:(e,t,n)=>{"use strict";e.exports=n(2269).style}}]);