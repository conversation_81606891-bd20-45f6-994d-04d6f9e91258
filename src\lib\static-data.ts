/**
 * مكتبة لقراءة البيانات الثابتة المحفوظة محلياً
 * تعمل حتى لو انتهت حدود Supabase
 */

import fs from 'fs';
import path from 'path';
import { Article, AITool, Service } from '@/types';

interface StaticMetadata {
  lastUpdate: string;
  counts: {
    articles: number;
    aiTools: number;
    services: number;
    pages: number;
  };
}

interface PageData {
  id: string;
  page_key: string;
  title_ar: string;
  content_ar: string;
  title_en?: string;
  content_en?: string;
  meta_description?: string;
  meta_description_en?: string;
  meta_keywords?: string;
  meta_keywords_en?: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

class StaticDataManager {
  private staticDir: string;
  private cache: Map<string, any> = new Map();

  constructor() {
    this.staticDir = path.join(process.cwd(), 'static-data');
  }

  private readJsonFile<T>(filename: string): T | null {
    try {
      const filePath = path.join(this.staticDir, filename);
      
      if (!fs.existsSync(filePath)) {
        console.warn(`⚠️ Static file not found: ${filename}`);
        return null;
      }

      const data = fs.readFileSync(filePath, 'utf-8');
      return JSON.parse(data);
    } catch (error) {
      console.error(`❌ Error reading static file ${filename}:`, error);
      return null;
    }
  }

  private getCachedData<T>(key: string, loader: () => T | null): T | null {
    if (this.cache.has(key)) {
      return this.cache.get(key);
    }

    const data = loader();
    if (data) {
      this.cache.set(key, data);
    }

    return data;
  }

  getArticles(): Article[] {
    return this.getCachedData('articles', () => 
      this.readJsonFile<Article[]>('articles.json')
    ) || [];
  }

  getArticleBySlug(slug: string): Article | null {
    const articles = this.getArticles();
    return articles.find(article => article.slug === slug) || null;
  }

  getAITools(): AITool[] {
    return this.getCachedData('ai-tools', () => 
      this.readJsonFile<AITool[]>('ai-tools.json')
    ) || [];
  }

  getAIToolBySlug(slug: string): AITool | null {
    const tools = this.getAITools();
    return tools.find(tool => tool.slug === slug) || null;
  }

  getServices(): Service[] {
    return this.getCachedData('services', () => 
      this.readJsonFile<Service[]>('services.json')
    ) || [];
  }

  getServiceById(id: string): Service | null {
    const services = this.getServices();
    return services.find(service => service.id === id) || null;
  }

  getPages(): PageData[] {
    return this.getCachedData('pages', () => 
      this.readJsonFile<PageData[]>('pages.json')
    ) || [];
  }

  getPageByKey(pageKey: string): PageData | null {
    const pages = this.getPages();
    return pages.find(page => page.page_key === pageKey) || null;
  }

  getMetadata(): StaticMetadata | null {
    return this.getCachedData('metadata', () => 
      this.readJsonFile<StaticMetadata>('metadata.json')
    );
  }

  // دوال مساعدة للفلترة والبحث
  getFeaturedArticles(limit: number = 5): Article[] {
    return this.getArticles()
      .filter((article: any) => article.featured)
      .slice(0, limit);
  }

  getLatestArticles(limit: number = 10): Article[] {
    return this.getArticles()
      .sort((a: any, b: any) => new Date(b.published_at || b.created_at).getTime() -
                     new Date(a.published_at || a.created_at).getTime())
      .slice(0, limit);
  }

  getAIToolsByCategory(category: string): AITool[] {
    return this.getAITools()
      .filter(tool => tool.category === category);
  }

  searchAITools(query: string): AITool[] {
    const searchTerm = query.toLowerCase();
    return this.getAITools()
      .filter(tool => 
        tool.name.toLowerCase().includes(searchTerm) ||
        tool.description.toLowerCase().includes(searchTerm) ||
        tool.category.toLowerCase().includes(searchTerm)
      );
  }

  getTopRatedAITools(limit: number = 10): AITool[] {
    return this.getAITools()
      .sort((a, b) => parseFloat(b.rating || '0') - parseFloat(a.rating || '0'))
      .slice(0, limit);
  }

  getFeaturedServices(): Service[] {
    return this.getServices()
      .filter((service: any) => service.featured)
      .sort((a: any, b: any) => (a.display_order || 999) - (b.display_order || 999));
  }

  // إحصائيات سريعة
  getStats() {
    const metadata = this.getMetadata();
    const articles = this.getArticles();
    const aiTools = this.getAITools();
    const services = this.getServices();

    return {
      lastUpdate: metadata?.lastUpdate || 'Unknown',
      counts: {
        articles: articles.length,
        aiTools: aiTools.length,
        services: services.length,
        publishedArticles: articles.filter((a: any) => a.status === 'published').length,
        featuredArticles: articles.filter((a: any) => a.featured).length,
        topRatedTools: aiTools.filter((t: any) => parseFloat(t.rating || '0') >= 4).length,
        freeTools: aiTools.filter((t: any) => t.pricing === 'free').length,
        activeServices: services.filter((s: any) => s.status === 'active').length
      }
    };
  }

  // تنظيف Cache
  clearCache() {
    this.cache.clear();
  }

  // التحقق من وجود البيانات الثابتة
  hasStaticData(): boolean {
    return fs.existsSync(this.staticDir) && 
           fs.existsSync(path.join(this.staticDir, 'metadata.json'));
  }
}

// إنشاء instance واحد للتطبيق
export const staticData = new StaticDataManager();

// دوال مساعدة للاستخدام المباشر
export const getStaticArticles = () => staticData.getArticles();
export const getStaticArticleBySlug = (slug: string) => staticData.getArticleBySlug(slug);
export const getStaticAITools = () => staticData.getAITools();
export const getStaticAIToolBySlug = (slug: string) => staticData.getAIToolBySlug(slug);
export const getStaticServices = () => staticData.getServices();
export const getStaticPages = () => staticData.getPages();
export const getStaticPageByKey = (key: string) => staticData.getPageByKey(key);
