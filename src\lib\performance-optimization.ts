/**
 * تحسينات الأداء لتقليل استهلاك Vercel
 */

// إعدادات Cache محسنة
export const CACHE_SETTINGS = {
  // الصفحة الرئيسية - تحديث كل 6 ساعات
  homepage: 21600,
  
  // صفحات المقالات وأدوات الذكاء الاصطناعي - تحديث كل أسبوع
  content: 604800,
  
  // الصفحات الثابتة - تحديث كل أسبوع
  static: 604800,
  
  // الصور - cache لمدة أسبوع
  images: 604800
};

// إعدادات لتقليل Function Invocations
export const OPTIMIZATION_CONFIG = {
  // تقليل عدد الطلبات للـ API
  batchSize: 50,
  
  // استخدام cache للبيانات المتكررة
  enableDataCache: true,
  
  // تأخير التحديثات غير الضرورية
  debounceTime: 1000,
  
  // تحسين الصور
  imageOptimization: {
    quality: 80,
    formats: ['webp'],
    sizes: [640, 750, 828, 1080, 1200]
  }
};

/**
 * دالة لتحسين طلبات قاعدة البيانات
 */
export function optimizeQuery(query: any) {
  // إضافة limit للطلبات لتقليل Data Transfer
  if (!query.limit) {
    query = query.limit(50);
  }
  
  // إضافة cache headers
  return query;
}

/**
 * دالة لتحسين استهلاك الصور
 */
export function optimizeImageUrl(url: string, width?: number, quality?: number) {
  if (!url) return url;
  
  // استخدام معاملات تحسين الصور
  const params = new URLSearchParams();
  if (width) params.set('w', width.toString());
  if (quality) params.set('q', (quality || 80).toString());
  
  const separator = url.includes('?') ? '&' : '?';
  return `${url}${separator}${params.toString()}`;
}

/**
 * دالة لتقليل عدد الطلبات المتكررة
 */
const cache = new Map();
export function memoize<T>(fn: Function, key: string, ttl: number = 300000): T | null {
  const cached = cache.get(key);
  if (cached && Date.now() - cached.timestamp < ttl) {
    return cached.data;
  }
  
  const result = fn();
  cache.set(key, { data: result, timestamp: Date.now() });
  return result;
}

/**
 * تنظيف Cache القديم
 */
export function cleanupCache() {
  const now = Date.now();
  // تحويل entries إلى array أولاً
  const entries = Array.from(cache.entries());

  for (const [key, value] of entries) {
    if (now - value.timestamp > 600000) { // 10 دقائق
      cache.delete(key);
    }
  }
}

// تشغيل تنظيف Cache كل 10 دقائق
if (typeof window === 'undefined') {
  setInterval(cleanupCache, 600000);
}
