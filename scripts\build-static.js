#!/usr/bin/env node

/**
 * سكريبت لبناء الموقع Static مع جلب البيانات من Supabase
 * يتم تشغيله كل 24 ساعة لتحديث المحتوى
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');

// قراءة متغيرات البيئة من .env.local
require('dotenv').config({ path: '.env.local' });

// إعدادات Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Supabase credentials not found');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fetchAllData() {
  console.log('🔄 Fetching all data from Supabase...');
  
  try {
    // جلب المقالات
    const { data: articles, error: articlesError } = await supabase
      .from('articles')
      .select('id, title, slug, excerpt, content, author, status, tags, seo_title, seo_description, reading_time, created_at, updated_at, published_at')
      .eq('status', 'published')
      .order('published_at', { ascending: false });

    if (articlesError) {
      console.error('❌ Error fetching articles:', articlesError);
    } else {
      console.log(`✅ Fetched ${articles?.length || 0} articles`);
    }

    // جلب أدوات الذكاء الاصطناعي
    const { data: aiTools, error: aiToolsError } = await supabase
      .from('ai_tools')
      .select('id, name, description, category, pricing, logo_url, website_url, slug, rating, features, pros, cons, use_cases, created_at, updated_at')
      .in('status', ['published', 'active'])
      .order('rating', { ascending: false })
      .order('created_at', { ascending: false });

    if (aiToolsError) {
      console.error('❌ Error fetching AI tools:', aiToolsError);
    } else {
      console.log(`✅ Fetched ${aiTools?.length || 0} AI tools`);
    }

    // جلب الخدمات
    const { data: services, error: servicesError } = await supabase
      .from('services')
      .select('id, name, description, category, status, featured, display_order, created_at')
      .eq('status', 'active')
      .order('display_order', { ascending: true });

    if (servicesError) {
      console.error('❌ Error fetching services:', servicesError);
    } else {
      console.log(`✅ Fetched ${services?.length || 0} services`);
    }

    // جلب الصفحات الثابتة
    const { data: pages, error: pagesError } = await supabase
      .from('site_pages')
      .select('*')
      .eq('is_active', true);

    if (pagesError) {
      console.error('❌ Error fetching pages:', pagesError);
    } else {
      console.log(`✅ Fetched ${pages?.length || 0} pages`);
    }

    return {
      articles: articles || [],
      aiTools: aiTools || [],
      services: services || [],
      pages: pages || [],
      timestamp: new Date().toISOString()
    };

  } catch (error) {
    console.error('💥 Error fetching data:', error);
    return null;
  }
}

async function saveStaticData(data) {
  console.log('💾 Saving static data...');
  
  const staticDir = path.join(process.cwd(), 'static-data');
  
  // إنشاء مجلد البيانات الثابتة
  if (!fs.existsSync(staticDir)) {
    fs.mkdirSync(staticDir, { recursive: true });
  }

  try {
    // حفظ البيانات في ملفات JSON
    fs.writeFileSync(
      path.join(staticDir, 'articles.json'),
      JSON.stringify(data.articles, null, 2)
    );

    fs.writeFileSync(
      path.join(staticDir, 'ai-tools.json'),
      JSON.stringify(data.aiTools, null, 2)
    );

    fs.writeFileSync(
      path.join(staticDir, 'services.json'),
      JSON.stringify(data.services, null, 2)
    );

    fs.writeFileSync(
      path.join(staticDir, 'pages.json'),
      JSON.stringify(data.pages, null, 2)
    );

    fs.writeFileSync(
      path.join(staticDir, 'metadata.json'),
      JSON.stringify({
        lastUpdate: data.timestamp,
        counts: {
          articles: data.articles.length,
          aiTools: data.aiTools.length,
          services: data.services.length,
          pages: data.pages.length
        }
      }, null, 2)
    );

    console.log('✅ Static data saved successfully');
    return true;
  } catch (error) {
    console.error('❌ Error saving static data:', error);
    return false;
  }
}

async function main() {
  console.log('🚀 Starting static build process...');
  console.log('📅 Timestamp:', new Date().toISOString());
  
  const data = await fetchAllData();
  
  if (!data) {
    console.error('❌ Failed to fetch data');
    process.exit(1);
  }

  const saved = await saveStaticData(data);
  
  if (!saved) {
    console.error('❌ Failed to save static data');
    process.exit(1);
  }

  console.log('🎉 Static build completed successfully!');
  console.log('📊 Summary:');
  console.log(`   - Articles: ${data.articles.length}`);
  console.log(`   - AI Tools: ${data.aiTools.length}`);
  console.log(`   - Services: ${data.services.length}`);
  console.log(`   - Pages: ${data.pages.length}`);
  console.log(`   - Last Update: ${data.timestamp}`);
}

// تشغيل السكريبت
if (require.main === module) {
  main().catch(console.error);
}

module.exports = { fetchAllData, saveStaticData };
