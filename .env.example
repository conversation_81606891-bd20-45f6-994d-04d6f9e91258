# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key

# Service Role Key for SSG Build Time (Required for Static Generation)
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key_for_build_time

# Site Configuration
NEXT_PUBLIC_SITE_URL=https://www.tflash.site
NEXT_PUBLIC_SITE_NAME=TechnoFlash

# ISR Configuration
NEXT_PUBLIC_REVALIDATE_TIME=86400

# Google AdSense Configuration
NEXT_PUBLIC_ADSENSE_PUBLISHER_ID=ca-pub-your_actual_publisher_id_here

# Google Analytics Configuration (if needed)
NEXT_PUBLIC_GA_MEASUREMENT_ID=G-your_ga_measurement_id_here

# Example:
# NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
# NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
# SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Instructions:
# 1. Copy this file to .env.local
# 2. Replace the values with your actual Supabase credentials
# 3. Get your credentials from: https://supabase.com/dashboard/project/your-project/settings/api
