import { NextResponse } from 'next/server';
import { usageMonitor, recordUsage } from '@/lib/usage-monitor';
import { supabaseCache } from '@/lib/supabase-cache';

/**
 * API لمراقبة الاستهلاك
 */
export async function GET() {
  try {
    const currentUsage = usageMonitor.getCurrentUsage();
    const alerts = usageMonitor.getAlerts();
    const suggestions = usageMonitor.getOptimizationSuggestions();
    const trend = usageMonitor.getUsageTrend(24);
    const prediction = usageMonitor.predictUsage(30);
    const cacheStats = supabaseCache.getStats();

    const response = {
      current: currentUsage,
      alerts,
      suggestions,
      trend: trend.slice(-10), // آخر 10 نقاط
      prediction,
      cache: {
        size: cacheStats.size,
        maxSize: cacheStats.maxSize,
        efficiency: calculateCacheEfficiency()
      },
      recommendations: getEmergencyRecommendations(currentUsage),
      timestamp: new Date().toISOString()
    };

    return NextResponse.json(response, {
      headers: {
        'Cache-Control': 'public, max-age=300' // 5 دقائق
      }
    });
  } catch (error) {
    console.error('Error in usage monitor API:', error);
    return NextResponse.json({ 
      error: 'Failed to get usage stats' 
    }, { status: 500 });
  }
}

/**
 * تسجيل استهلاك جديد
 */
export async function POST(request: Request) {
  try {
    const stats = await request.json();
    recordUsage(stats);

    return NextResponse.json({ 
      success: true, 
      message: 'Usage recorded successfully' 
    });
  } catch (error) {
    console.error('Error recording usage:', error);
    return NextResponse.json({ 
      error: 'Failed to record usage' 
    }, { status: 500 });
  }
}

function calculateCacheEfficiency(): number {
  // حساب كفاءة Cache بناءً على الاستخدام
  const stats = supabaseCache.getStats();
  return Math.min(stats.size / stats.maxSize, 1) * 100;
}

function getEmergencyRecommendations(currentUsage: any): string[] {
  if (!currentUsage) return [];

  const recommendations: string[] = [];

  // توصيات طارئة إذا كان الاستهلاك مرتفع جداً
  if (currentUsage.supabase?.egress > 4500) { // 4.5GB من 5GB
    recommendations.push('🚨 URGENT: Implement immediate caching for all database queries');
    recommendations.push('🚨 URGENT: Reduce query frequency and use pagination');
    recommendations.push('🚨 URGENT: Consider upgrading Supabase plan');
  }

  if (currentUsage.vercel?.isrWrites > 180000) { // 180K من 200K
    recommendations.push('🚨 URGENT: Increase ISR revalidation time to 1 week');
    recommendations.push('🚨 URGENT: Disable ISR for non-critical pages');
    recommendations.push('🚨 URGENT: Use static export for unchanging content');
  }

  return recommendations;
}
