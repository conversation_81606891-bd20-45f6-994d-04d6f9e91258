"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/services/page",{

/***/ "(app-pages-browser)/./src/components/ServicesSection.tsx":
/*!********************************************!*\
  !*** ./src/components/ServicesSection.tsx ***!
  \********************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ServicesSection: () => (/* binding */ ServicesSection)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(app-pages-browser)/./node_modules/next/dist/client/app-dir/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ServiceCard__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ServiceCard */ \"(app-pages-browser)/./src/components/ServiceCard.tsx\");\n/* __next_internal_client_entry_do_not_use__ ServicesSection auto */ \n\n\nfunction ServicesSection(param) {\n    let { services, title = \"خدماتنا\", showViewAll = true, variant = 'default', maxItems } = param;\n    // تحديد عدد الخدمات المعروضة\n    const displayServices = maxItems ? services.slice(0, maxItems) : services;\n    if (!services || services.length === 0) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n            className: \"py-16 bg-gray-50\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"container mx-auto px-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"max-w-2xl mx-auto\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600 mb-4\",\n                                    children: \"حاليًا، لا نقدم أي خدمات مدفوعة أو منتجات للبيع.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 32,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-gray-600\",\n                                    children: \"الموقع مخصص لتقديم محتوى تثقيفي وتعليمي مجاني عن تقنيات وأدوات الذكاء الاصطناعي.\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 35,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 31,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 29,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                lineNumber: 28,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n            lineNumber: 27,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n        className: \"py-16 bg-gray-50\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"container mx-auto px-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center mb-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                            className: \"text-3xl font-bold text-gray-900 mb-4\",\n                            children: title\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 50,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600 max-w-2xl mx-auto\",\n                            children: \"نقدم مجموعة متنوعة من الخدمات التقنية المتطورة لمساعدتك في تحقيق أهدافك الرقمية\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 51,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 49,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-12\",\n                    children: displayServices.map((service)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ServiceCard__WEBPACK_IMPORTED_MODULE_2__.ServiceCard, {\n                            service: service,\n                            variant: service.featured ? 'featured' : variant\n                        }, service.id, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 59,\n                            columnNumber: 13\n                        }, this))\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 57,\n                    columnNumber: 9\n                }, this),\n                showViewAll && services.length > (maxItems || 0) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                        href: \"/services\",\n                        className: \"inline-block bg-primary text-white px-8 py-3 rounded-lg font-medium hover:bg-primary/90 transition-colors duration-200\",\n                        children: \"عرض جميع الخدمات\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                        lineNumber: 70,\n                        columnNumber: 13\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 69,\n                    columnNumber: 11\n                }, this),\n                services.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mt-16 grid grid-cols-1 md:grid-cols-3 gap-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-primary mb-2\",\n                                    children: services.length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 83,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600\",\n                                    children: \"خدمة متاحة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 86,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 82,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-primary mb-2\",\n                                    children: services.filter((s)=>s.featured).length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600\",\n                                    children: \"خدمة مميزة\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 88,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-3xl font-bold text-primary mb-2\",\n                                    children: services.filter((s)=>s.pricing_type === 'free').length\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 95,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-gray-600\",\n                                    children: \"خدمة مجانية\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                                    lineNumber: 98,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                            lineNumber: 94,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n                    lineNumber: 81,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n            lineNumber: 47,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\components\\\\ServicesSection.tsx\",\n        lineNumber: 46,\n        columnNumber: 5\n    }, this);\n}\n_c = ServicesSection;\nvar _c;\n$RefreshReg$(_c, \"ServicesSection\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ServicesSection.tsx\n"));

/***/ })

});