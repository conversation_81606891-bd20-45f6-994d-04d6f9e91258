/**
 * نظام Cache محلي لتقليل استهلاك Supabase Egress
 */

interface CacheItem {
  data: any;
  timestamp: number;
  ttl: number;
}

class SupabaseCache {
  private cache = new Map<string, CacheItem>();
  private maxSize = 100; // حد أقصى للعناصر المحفوظة

  set(key: string, data: any, ttlSeconds: number = 1800) { // 30 دقيقة افتراضي
    // تنظيف Cache إذا وصل للحد الأقصى
    if (this.cache.size >= this.maxSize) {
      this.cleanup();
    }

    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl: ttlSeconds * 1000
    });
  }

  get(key: string): any | null {
    const item = this.cache.get(key);
    
    if (!item) {
      return null;
    }

    // التحقق من انتهاء صلاحية Cache
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return null;
    }

    return item.data;
  }

  has(key: string): boolean {
    const item = this.cache.get(key);
    
    if (!item) {
      return false;
    }

    // التحقق من انتهاء صلاحية Cache
    if (Date.now() - item.timestamp > item.ttl) {
      this.cache.delete(key);
      return false;
    }

    return true;
  }

  delete(key: string): void {
    this.cache.delete(key);
  }

  clear(): void {
    this.cache.clear();
  }

  cleanup(): void {
    const now = Date.now();
    const toDelete: string[] = [];

    // تحويل entries إلى array أولاً
    const entries = Array.from(this.cache.entries());

    for (const [key, item] of entries) {
      if (now - item.timestamp > item.ttl) {
        toDelete.push(key);
      }
    }

    toDelete.forEach(key => this.cache.delete(key));

    // إذا لم يتم حذف عناصر كافية، احذف الأقدم
    if (this.cache.size >= this.maxSize) {
      const entries = Array.from(this.cache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      const toRemove = entries.slice(0, Math.floor(this.maxSize * 0.3));
      toRemove.forEach(([key]) => this.cache.delete(key));
    }
  }

  getStats() {
    return {
      size: this.cache.size,
      maxSize: this.maxSize,
      keys: Array.from(this.cache.keys())
    };
  }
}

// إنشاء instance واحد للتطبيق
export const supabaseCache = new SupabaseCache();

/**
 * دالة مساعدة لتنفيذ استعلامات مع Cache
 */
export async function cachedSupabaseQuery<T>(
  key: string,
  queryFn: () => Promise<T>,
  ttlSeconds: number = 1800
): Promise<T> {
  // التحقق من وجود البيانات في Cache
  if (supabaseCache.has(key)) {
    console.log(`📦 Cache hit for: ${key}`);
    return supabaseCache.get(key);
  }

  console.log(`🔄 Cache miss for: ${key}, fetching from database...`);
  
  try {
    // تنفيذ الاستعلام
    const data = await queryFn();
    
    // حفظ النتيجة في Cache
    supabaseCache.set(key, data, ttlSeconds);
    
    console.log(`✅ Cached result for: ${key}`);
    return data;
  } catch (error) {
    console.error(`❌ Error in cached query for ${key}:`, error);
    throw error;
  }
}

/**
 * دالة لإنشاء مفاتيح Cache ذكية
 */
export function createCacheKey(table: string, params: Record<string, any> = {}): string {
  const sortedParams = Object.keys(params)
    .sort()
    .map(key => `${key}:${params[key]}`)
    .join('|');
  
  return `${table}${sortedParams ? `|${sortedParams}` : ''}`;
}

/**
 * تنظيف Cache تلقائياً كل 10 دقائق
 */
if (typeof window === 'undefined') {
  setInterval(() => {
    supabaseCache.cleanup();
    console.log('🧹 Cache cleanup completed');
  }, 600000); // 10 دقائق
}
