(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[785],{1463:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>d});var r=s(5155),a=s(2115),n=s(6874),i=s.n(n);function d(){let[e,t]=(0,a.useState)([]),[s,n]=(0,a.useState)(!0),[d,c]=(0,a.useState)(""),l=async()=>{try{n(!0);let e=await fetch("/api/pages?include_inactive=true"),s=await e.json();s.success?t(s.data):c(s.message||"فشل في جلب الصفحات")}catch(e){console.error("خطأ في جلب الصفحات:",e),c("حدث خطأ أثناء جلب الصفحات")}finally{n(!1)}},o=async(t,s)=>{try{var r,a;let n=await fetch("/api/pages/".concat(t),{method:"PUT",headers:{"Content-Type":"application/json"},body:JSON.stringify({is_active:!s,title_ar:null==(r=e.find(e=>e.id===t))?void 0:r.title_ar,content_ar:null==(a=e.find(e=>e.id===t))?void 0:a.content_ar})}),i=await n.json();i.success?await l():alert("فشل في تحديث حالة الصفحة: "+i.message)}catch(e){console.error("خطأ في تحديث الصفحة:",e),alert("حدث خطأ أثناء تحديث الصفحة")}},h=async(e,t)=>{if(confirm('هل أنت متأكد من حذف صفحة "'.concat(t,'"؟')))try{let t=await fetch("/api/pages/".concat(e),{method:"DELETE"}),s=await t.json();s.success?await l():alert("فشل في حذف الصفحة: "+s.message)}catch(e){console.error("خطأ في حذف الصفحة:",e),alert("حدث خطأ أثناء حذف الصفحة")}};return((0,a.useEffect)(()=>{l()},[]),s)?(0,r.jsx)("div",{className:"min-h-screen bg-dark-background p-6",children:(0,r.jsx)("div",{className:"flex justify-center items-center h-64",children:(0,r.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"})})}):(0,r.jsx)("div",{className:"min-h-screen bg-dark-background p-6",children:(0,r.jsxs)("div",{className:"container mx-auto",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-8",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"إدارة الصفحات"}),(0,r.jsx)("p",{className:"text-dark-text-secondary",children:"إدارة محتوى الصفحات الثابتة في الموقع"})]}),(0,r.jsxs)("div",{className:"flex gap-4",children:[(0,r.jsx)(i(),{href:"/admin/pages/new",className:"bg-primary hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"إضافة صفحة جديدة"}),(0,r.jsx)(i(),{href:"/admin",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:"العودة للوحة التحكم"})]})]}),d&&(0,r.jsx)("div",{className:"bg-red-500/10 border border-red-500/20 rounded-lg p-4 mb-6",children:(0,r.jsx)("p",{className:"text-red-400",children:d})}),(0,r.jsx)("div",{className:"bg-dark-card rounded-lg border border-gray-800 overflow-hidden",children:(0,r.jsx)("div",{className:"overflow-x-auto",children:(0,r.jsxs)("table",{className:"w-full",children:[(0,r.jsx)("thead",{className:"bg-dark-background",children:(0,r.jsxs)("tr",{children:[(0,r.jsx)("th",{className:"text-right p-4 text-white font-medium",children:"العنوان"}),(0,r.jsx)("th",{className:"text-right p-4 text-white font-medium",children:"مفتاح الصفحة"}),(0,r.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"الحالة"}),(0,r.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"الترتيب"}),(0,r.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"آخر تحديث"}),(0,r.jsx)("th",{className:"text-center p-4 text-white font-medium",children:"الإجراءات"})]})}),(0,r.jsx)("tbody",{children:e.map(e=>(0,r.jsxs)("tr",{className:"border-t border-gray-800 hover:bg-dark-background/50",children:[(0,r.jsx)("td",{className:"p-4",children:(0,r.jsxs)("div",{children:[(0,r.jsx)("h3",{className:"text-white font-medium",children:e.title_ar}),e.meta_description&&(0,r.jsx)("p",{className:"text-dark-text-secondary text-sm mt-1 line-clamp-2",children:e.meta_description})]})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsx)("code",{className:"bg-dark-background px-2 py-1 rounded text-primary text-sm",children:e.page_key})}),(0,r.jsx)("td",{className:"p-4 text-center",children:(0,r.jsx)("button",{onClick:()=>o(e.id,e.is_active),className:"px-3 py-1 rounded-full text-sm font-medium transition-colors duration-300 ".concat(e.is_active?"bg-green-500/20 text-green-400 hover:bg-green-500/30":"bg-red-500/20 text-red-400 hover:bg-red-500/30"),children:e.is_active?"نشط":"غير نشط"})}),(0,r.jsx)("td",{className:"p-4 text-center",children:(0,r.jsx)("span",{className:"text-dark-text-secondary",children:e.display_order})}),(0,r.jsx)("td",{className:"p-4 text-center",children:(0,r.jsx)("span",{className:"text-dark-text-secondary text-sm",children:new Date(e.updated_at).toLocaleDateString("ar-SA")})}),(0,r.jsx)("td",{className:"p-4",children:(0,r.jsxs)("div",{className:"flex justify-center gap-2",children:[(0,r.jsx)(i(),{href:"/page/".concat(e.page_key),target:"_blank",className:"bg-blue-500/20 hover:bg-blue-500/30 text-blue-400 p-2 rounded transition-colors duration-300",title:"عرض الصفحة",children:(0,r.jsxs)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:[(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M15 12a3 3 0 11-6 0 3 3 0 016 0z"}),(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"})]})}),(0,r.jsx)(i(),{href:"/admin/pages/edit/".concat(e.id),className:"bg-yellow-500/20 hover:bg-yellow-500/30 text-yellow-400 p-2 rounded transition-colors duration-300",title:"تعديل الصفحة",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"})})}),(0,r.jsx)("button",{onClick:()=>h(e.id,e.title_ar),className:"bg-red-500/20 hover:bg-red-500/30 text-red-400 p-2 rounded transition-colors duration-300",title:"حذف الصفحة",children:(0,r.jsx)("svg",{className:"w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"})})})]})})]},e.id))})]})})}),0===e.length&&!s&&(0,r.jsxs)("div",{className:"text-center py-12",children:[(0,r.jsx)("div",{className:"w-16 h-16 bg-dark-card rounded-full flex items-center justify-center mx-auto mb-4",children:(0,r.jsx)("svg",{className:"w-8 h-8 text-dark-text-secondary",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,r.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})})}),(0,r.jsx)("h3",{className:"text-xl font-medium text-white mb-2",children:"لا توجد صفحات"}),(0,r.jsx)("p",{className:"text-dark-text-secondary mb-6",children:"لم يتم إنشاء أي صفحات بعد"}),(0,r.jsx)(i(),{href:"/admin/pages/new",className:"bg-primary hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300 inline-block",children:"إنشاء أول صفحة"})]})]})})}},8901:(e,t,s)=>{Promise.resolve().then(s.bind(s,1463))}},e=>{var t=t=>e(e.s=t);e.O(0,[6874,8441,1684,7358],()=>t(8901)),_N_E=e.O()}]);