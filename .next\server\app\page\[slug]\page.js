"use strict";(()=>{var e={};e.id=6304,e.ids=[6304],e.modules={163:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return n}});let n=r(71042).unstable_rethrow;("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},3295:e=>{e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{e.exports=require("punycode")},19121:e=>{e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},25221:(e,t,r)=>{r.d(t,{Oh:()=>u,mT:()=>p,getStaticPages:()=>c,Tp:()=>d});let n=require("fs");var a=r.n(n),i=r(33873),o=r.n(i);class s{constructor(){this.cache=new Map,this.staticDir=o().join(process.cwd(),"static-data")}readJsonFile(e){try{let t=o().join(this.staticDir,e);if(!a().existsSync(t))return null;let r=a().readFileSync(t,"utf-8");return JSON.parse(r)}catch(t){return console.error(`❌ Error reading static file ${e}:`,t),null}}getCachedData(e,t){if(this.cache.has(e))return this.cache.get(e);let r=t();return r&&this.cache.set(e,r),r}getArticles(){return this.getCachedData("articles",()=>this.readJsonFile("articles.json"))||[]}getArticleBySlug(e){return this.getArticles().find(t=>t.slug===e)||null}getAITools(){return this.getCachedData("ai-tools",()=>this.readJsonFile("ai-tools.json"))||[]}getAIToolBySlug(e){return this.getAITools().find(t=>t.slug===e)||null}getServices(){return this.getCachedData("services",()=>this.readJsonFile("services.json"))||[]}getServiceById(e){return this.getServices().find(t=>t.id===e)||null}getPages(){return this.getCachedData("pages",()=>this.readJsonFile("pages.json"))||[]}getPageByKey(e){return this.getPages().find(t=>t.page_key===e)||null}getMetadata(){return this.getCachedData("metadata",()=>this.readJsonFile("metadata.json"))}getFeaturedArticles(e=5){return this.getArticles().filter(e=>e.featured).slice(0,e)}getLatestArticles(e=10){return this.getArticles().sort((e,t)=>new Date(t.published_at||t.created_at).getTime()-new Date(e.published_at||e.created_at).getTime()).slice(0,e)}getAIToolsByCategory(e){return this.getAITools().filter(t=>t.category===e)}searchAITools(e){let t=e.toLowerCase();return this.getAITools().filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.category.toLowerCase().includes(t))}getTopRatedAITools(e=10){return this.getAITools().sort((e,t)=>parseFloat(t.rating||"0")-parseFloat(e.rating||"0")).slice(0,e)}getFeaturedServices(){return this.getServices().filter(e=>e.featured).sort((e,t)=>(e.display_order||999)-(t.display_order||999))}getStats(){let e=this.getMetadata(),t=this.getArticles(),r=this.getAITools(),n=this.getServices();return{lastUpdate:e?.lastUpdate||"Unknown",counts:{articles:t.length,aiTools:r.length,services:n.length,publishedArticles:t.filter(e=>"published"===e.status).length,featuredArticles:t.filter(e=>e.featured).length,topRatedTools:r.filter(e=>parseFloat(e.rating||"0")>=4).length,freeTools:r.filter(e=>"free"===e.pricing).length,activeServices:n.filter(e=>"active"===e.status).length}}}clearCache(){this.cache.clear()}hasStaticData(){return a().existsSync(this.staticDir)&&a().existsSync(o().join(this.staticDir,"metadata.json"))}}let l=new s,u=()=>l.getAITools(),d=()=>l.getServices(),c=()=>l.getPages(),p=e=>l.getPageByKey(e)},27910:e=>{e.exports=require("stream")},29294:e=>{e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{e.exports=require("path")},34631:e=>{e.exports=require("tls")},39916:(e,t,r)=>{var n=r(97576);r.o(n,"notFound")&&r.d(t,{notFound:function(){return n.notFound}})},48976:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`forbidden()` is experimental and only allowed to be enabled when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E488",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"forbidden",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},55511:e=>{e.exports=require("crypto")},55591:e=>{e.exports=require("https")},55778:(e,t,r)=>{r.r(t),r.d(t,{default:()=>p,dynamic:()=>g,generateMetadata:()=>d,generateStaticParams:()=>c,revalidate:()=>f});var n=r(37413),a=r(39916),i=r(4536),o=r.n(i),s=r(56621),l=r(25221);async function u(e){try{let{data:t,error:r}=await s.ND.from("site_pages").select("*").eq("page_key",e).eq("is_active",!0).single();if(!r&&t)return(0,s.EI)(t);let n=(0,l.mT)(e);if(n)return n;return console.error("❌ Page not found in static data:",e),null}catch(t){return console.error("❌ Error fetching page data, trying static fallback:",t),(0,l.mT)(e)||null}}async function d({params:e}){let{slug:t}=await e,r=await u(t);if(!r)return{title:"صفحة غير موجودة - TechnoFlash",description:"الصفحة المطلوبة غير موجودة"};let n=["about-us","contact-us","privacy-policy","terms-of-use"].includes(t),a=n&&r.title_en||r.title_ar,i=n?r.meta_description_en||r.meta_description||a:r.meta_description||a;return{title:`${a} - TechnoFlash`,description:i,keywords:n?r.meta_keywords_en||r.meta_keywords||"":r.meta_keywords||"",openGraph:{title:`${a} - TechnoFlash`,description:i,type:"website",locale:n?"en_US":"ar_SA"},twitter:{card:"summary_large_image",title:`${a} - TechnoFlash`,description:i}}}async function c(){try{let{data:e,error:t}=await s.ND.from("site_pages").select("page_key").eq("is_active",!0);if(!t&&e&&e.length>0)return e.map(e=>({slug:e.page_key}));let{getStaticPages:n}=await Promise.resolve().then(r.bind(r,25221));return n().map(e=>({slug:e.page_key}))}catch(e){console.error("❌ Error in generateStaticParams, using static fallback:",e);try{let{getStaticPages:e}=await Promise.resolve().then(r.bind(r,25221));return e().map(e=>({slug:e.page_key}))}catch(e){return console.error("❌ Static fallback also failed:",e),[]}}}async function p({params:e}){let{slug:t}=await e,r=await u(t);r||(0,a.notFound)();let i=["about-us","contact-us","privacy-policy","terms-of-use"].includes(t);return(0,n.jsx)("div",{className:"min-h-screen py-20 px-4 bg-dark-background",children:(0,n.jsxs)("div",{className:"container mx-auto max-w-4xl",children:[(0,n.jsxs)("header",{className:"text-center mb-12",children:[(0,n.jsx)("h1",{className:"text-4xl md:text-5xl font-bold text-white mb-4",children:i&&r.title_en||r.title_ar}),(i&&r.meta_description_en||r.meta_description)&&(0,n.jsx)("p",{className:"text-xl text-dark-text-secondary max-w-2xl mx-auto",children:i&&r.meta_description_en||r.meta_description})]}),(0,n.jsx)("main",{className:"bg-dark-card rounded-xl p-8 border border-gray-800",children:(0,n.jsx)("div",{className:"prose prose-lg prose-invert max-w-none",style:{color:"#e5e7eb",lineHeight:"1.8"},dangerouslySetInnerHTML:{__html:i&&r.content_en||r.content_ar}})}),(0,n.jsx)("footer",{className:"mt-12 text-center",children:(0,n.jsxs)("div",{className:"bg-dark-card rounded-lg p-6 border border-gray-800",children:[(0,n.jsxs)("p",{className:"text-dark-text-secondary text-sm mb-4",children:[i?"Last updated: ":"آخر تحديث: ",new Date(r.updated_at).toLocaleDateString(i?"en-US":"ar-SA",{year:"numeric",month:"long",day:"numeric"})]}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center items-center",children:[(0,n.jsx)(o(),{href:"/",className:"bg-primary hover:bg-blue-600 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:i?"Back to Home":"العودة للرئيسية"}),(0,n.jsx)(o(),{href:"/page/contact-us",className:"border border-gray-600 hover:border-gray-500 text-white px-6 py-3 rounded-lg font-medium transition-colors duration-300",children:i?"Contact Us":"تواصل معنا"})]})]})})]})})}let f=604800,g="force-static"},56621:(e,t,r)=>{r.d(t,{EI:()=>s,ND:()=>l});var n=r(66437);let a="https://zgktrwpladrkhhemhnni.supabase.co",i="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04";if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!i)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");function o(e){if(!e)return e;try{if(e.includes("\\u"))return JSON.parse(`"${e}"`);return e}catch(t){return e}}function s(e){if(!e)return e;let t={...e};for(let e in t)"string"==typeof t[e]?t[e]=o(t[e]):Array.isArray(t[e])&&(t[e]=t[e].map(e=>"string"==typeof e?o(e):e));return t}let l=(0,n.UU)(a,i,{db:{schema:"public"},auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}})},62765:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"notFound",{enumerable:!0,get:function(){return a}});let n=""+r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE+";404";function a(){let e=Object.defineProperty(Error(n),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});throw e.digest=n,e}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},63033:e=>{e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},64213:(e,t,r)=>{r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>c,pages:()=>d,routeModule:()=>p,tree:()=>u});var n=r(65239),a=r(48088),i=r(88170),o=r.n(i),s=r(30893),l={};for(let e in s)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>s[e]);r.d(t,l);let u={children:["",{children:["page",{children:["[slug]",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,55778)),"C:\\Users\\<USER>\\4\\src\\app\\page\\[slug]\\page.tsx"]}]},{}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,94431)),"C:\\Users\\<USER>\\4\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(r.bind(r,67393)),"C:\\Users\\<USER>\\4\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(r.bind(r,54413)),"C:\\Users\\<USER>\\4\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(r.t.bind(r,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(r.t.bind(r,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,d=["C:\\Users\\<USER>\\4\\src\\app\\page\\[slug]\\page.tsx"],c={require:r,loadChunk:()=>Promise.resolve()},p=new n.AppPageRouteModule({definition:{kind:a.RouteKind.APP_PAGE,page:"/page/[slug]/page",pathname:"/page/[slug]",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:u}})},70899:(e,t,r)=>{function n(){throw Object.defineProperty(Error("`unauthorized()` is experimental and only allowed to be used when `experimental.authInterrupts` is enabled."),"__NEXT_ERROR_CODE",{value:"E411",enumerable:!1,configurable:!0})}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unauthorized",{enumerable:!0,get:function(){return n}}),r(8704).HTTP_ERROR_FALLBACK_ERROR_CODE,("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},71042:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"unstable_rethrow",{enumerable:!0,get:function(){return function e(t){if((0,o.isNextRouterError)(t)||(0,i.isBailoutToCSRError)(t)||(0,l.isDynamicServerError)(t)||(0,s.isDynamicPostpone)(t)||(0,a.isPostpone)(t)||(0,n.isHangingPromiseRejectionError)(t))throw t;t instanceof Error&&"cause"in t&&e(t.cause)}}});let n=r(68388),a=r(52637),i=r(51846),o=r(31162),s=r(84971),l=r(98479);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},74075:e=>{e.exports=require("zlib")},79428:e=>{e.exports=require("buffer")},79551:e=>{e.exports=require("url")},81630:e=>{e.exports=require("http")},86897:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{getRedirectError:function(){return o},getRedirectStatusCodeFromError:function(){return c},getRedirectTypeFromError:function(){return d},getURLFromRedirectError:function(){return u},permanentRedirect:function(){return l},redirect:function(){return s}});let n=r(52836),a=r(49026),i=r(19121).actionAsyncStorage;function o(e,t,r){void 0===r&&(r=n.RedirectStatusCode.TemporaryRedirect);let i=Object.defineProperty(Error(a.REDIRECT_ERROR_CODE),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return i.digest=a.REDIRECT_ERROR_CODE+";"+t+";"+e+";"+r+";",i}function s(e,t){var r;throw null!=t||(t=(null==i||null==(r=i.getStore())?void 0:r.isAction)?a.RedirectType.push:a.RedirectType.replace),o(e,t,n.RedirectStatusCode.TemporaryRedirect)}function l(e,t){throw void 0===t&&(t=a.RedirectType.replace),o(e,t,n.RedirectStatusCode.PermanentRedirect)}function u(e){return(0,a.isRedirectError)(e)?e.digest.split(";").slice(2,-2).join(";"):null}function d(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return e.digest.split(";",2)[1]}function c(e){if(!(0,a.isRedirectError)(e))throw Object.defineProperty(Error("Not a redirect error"),"__NEXT_ERROR_CODE",{value:"E260",enumerable:!1,configurable:!0});return Number(e.digest.split(";").at(-2))}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},91645:e=>{e.exports=require("net")},94735:e=>{e.exports=require("events")},97576:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var r in t)Object.defineProperty(e,r,{enumerable:!0,get:t[r]})}(t,{ReadonlyURLSearchParams:function(){return d},RedirectType:function(){return a.RedirectType},forbidden:function(){return o.forbidden},notFound:function(){return i.notFound},permanentRedirect:function(){return n.permanentRedirect},redirect:function(){return n.redirect},unauthorized:function(){return s.unauthorized},unstable_rethrow:function(){return l.unstable_rethrow}});let n=r(86897),a=r(49026),i=r(62765),o=r(48976),s=r(70899),l=r(163);class u extends Error{constructor(){super("Method unavailable on `ReadonlyURLSearchParams`. Read more: https://nextjs.org/docs/app/api-reference/functions/use-search-params#updating-searchparams")}}class d extends URLSearchParams{append(){throw new u}delete(){throw new u}set(){throw new u}sort(){throw new u}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),n=t.X(0,[2714,6437,6656],()=>r(64213));module.exports=n})();