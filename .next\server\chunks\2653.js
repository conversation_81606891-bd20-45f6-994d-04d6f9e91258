exports.id=2653,exports.ids=[2653],exports.modules={20769:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>a});var s=t(60687),l=t(63213),i=t(16189);function a({children:e}){let{user:r,loading:t}=(0,l.A)();return((0,i.useRouter)(),t)?(0,s.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("div",{className:"inline-block animate-spin rounded-full h-12 w-12 border-b-2 border-primary mb-4"}),(0,s.jsx)("p",{className:"text-dark-text-secondary",children:"جاري التحقق من صلاحيات الوصول..."})]})}):r?(0,s.jsx)(s.Fragment,{children:e}):null}t(43210)},33134:(e,r,t)=>{Promise.resolve().then(t.bind(t,67083))},33908:(e,r,t)=>{"use strict";t.d(r,{A:()=>o});var s=t(60687),l=t(43210),i=t(16391),a=t(37590),n=t(41146);function o({articleId:e,images:r,onImagesChange:t,onImageInsert:o,maxImages:c=20,allowedTypes:d=["image/jpeg","image/png","image/webp","image/gif"]}){let[h,m]=(0,l.useState)(!1),[g,u]=(0,l.useState)({}),[x,p]=(0,l.useState)(null),b=(0,l.useRef)(null),y=(0,l.useCallback)(async s=>{if(0===s.length)return;if(r.length+s.length>c)return void a.Ay.error(`يمكن رفع ${c} صورة كحد أقصى`);m(!0);let l=[];try{for(let t=0;t<s.length;t++){let o=s[t];if(!d.includes(o.type)){a.Ay.error(`نوع الملف ${o.type} غير مدعوم`);continue}if(o.size>5242880){a.Ay.error(`حجم الملف ${o.name} كبير جداً (الحد الأقصى 5MB)`);continue}let c=`${Date.now()}-${t}`;u(e=>({...e,[c]:0}));try{u(e=>({...e,[c]:20}));let t=await f(o);u(e=>({...e,[c]:40}));let s=(0,n.nw)(o.name,`articles/${e||"temp"}`);u(e=>({...e,[c]:60}));let{data:a,error:d}=await i.ND.storage.from("article-images").upload(s,t,{cacheControl:"3600",upsert:!1});if(d)throw console.error("Upload error:",d),Error(`فشل في رفع الصورة: ${d.message}`);u(e=>({...e,[c]:80}));let{data:h}=i.ND.storage.from("article-images").getPublicUrl(s);if(!h.publicUrl)throw Error("فشل في الحصول على رابط الصورة");let m=await j(t);u(e=>({...e,[c]:90}));let g={id:crypto.randomUUID(),image_url:h.publicUrl,image_path:s,alt_text:o.name.split(".")[0].replace(/[^a-zA-Z0-9\u0600-\u06FF\s]/g,""),caption:"",file_size:t.size,mime_type:t.type,width:m.width,height:m.height,display_order:r.length+l.length};l.push(g),u(e=>({...e,[c]:100}))}catch(r){console.error("Error uploading image:",r);let e=r instanceof Error?r.message:"خطأ غير معروف";a.Ay.error(`فشل في رفع الصورة ${o.name}: ${e}`),u(e=>{let r={...e};return delete r[c],r})}}if(l.length>0){let e=[...r,...l];t(e),a.Ay.success(`تم رفع ${l.length} صورة بنجاح`)}}catch(e){console.error("Error in file upload:",e),a.Ay.error("حدث خطأ أثناء رفع الصور")}finally{m(!1),u({})}},[r,t,e,c,d]),f=async e=>new Promise((r,t)=>{if(e.size<1048576)return void r(e);let s=document.createElement("canvas"),l=s.getContext("2d"),i=new Image;i.onload=()=>{try{let{width:t,height:a}=i;if(t>1920||a>1080){let e=Math.min(1920/t,1080/a);t=Math.round(t*e),a=Math.round(a*e)}if(s.width=t,s.height=a,l)l.drawImage(i,0,0,t,a);else throw Error("فشل في إنشاء سياق الرسم");s.toBlob(t=>{if(t){let s=new File([t],e.name,{type:e.type,lastModified:Date.now()});r(s)}else r(e)},e.type,.8)}catch(t){console.error("خطأ في ضغط الصورة:",t),r(e)}},i.onerror=()=>{console.error("فشل في تحميل الصورة للضغط"),r(e)};try{i.src=URL.createObjectURL(e)}catch(t){console.error("فشل في إنشاء URL للصورة:",t),r(e)}}),j=async e=>new Promise((r,t)=>{let s=new Image;s.onload=()=>{URL.revokeObjectURL(s.src),r({width:s.width,height:s.height})},s.onerror=()=>{URL.revokeObjectURL(s.src),console.error("فشل في قراءة أبعاد الصورة"),r({width:800,height:600})};try{s.src=URL.createObjectURL(e)}catch(e){console.error("فشل في إنشاء URL للصورة:",e),r({width:800,height:600})}}),v=async s=>{let l=r[s];if(confirm("هل أنت متأكد من حذف هذه الصورة؟"))try{if(l.image_path){let{error:e}=await i.ND.storage.from("article-images").remove([l.image_path]);e&&console.error("Storage deletion error:",e)}if(e&&l.id){let{error:e}=await i.ND.from("article_images").delete().eq("id",l.id);e&&console.error("Database deletion error:",e)}let n=r.filter((e,r)=>r!==s);t(n),a.Ay.success("تم حذف الصورة بنجاح")}catch(r){console.error("Error deleting image:",r);let e=r instanceof Error?r.message:"خطأ غير معروف";a.Ay.error(`فشل في حذف الصورة: ${e}`)}},w=e=>{p(e)},N=e=>{e.preventDefault()},D=(e,s)=>{if(e.preventDefault(),null===x||x===s)return;let l=[...r],i=l[x];l.splice(x,1),l.splice(s,0,i),l.forEach((e,r)=>{e.display_order=r}),t(l),p(null)},R=(e,s)=>{let l=[...r];l[e].caption=s,t(l)},U=(e,s)=>{let l=[...r];l[e].alt_text=s,t(l)},$=e=>{o&&o(e.image_url,e.caption)};return(0,s.jsxs)("div",{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"border-2 border-dashed border-gray-600 rounded-lg p-6 text-center hover:border-primary transition-colors",onDragOver:e=>{e.preventDefault(),e.currentTarget.classList.add("border-primary","bg-primary/10")},onDragLeave:e=>{e.preventDefault(),e.currentTarget.classList.remove("border-primary","bg-primary/10")},onDrop:e=>{e.preventDefault(),e.currentTarget.classList.remove("border-primary","bg-primary/10");let r=e.dataTransfer.files;r.length>0&&y(r)},children:[(0,s.jsx)("input",{ref:b,type:"file",multiple:!0,accept:d.join(","),onChange:e=>e.target.files&&y(e.target.files),className:"hidden"}),(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)("div",{className:"text-4xl text-gray-400",children:"\uD83D\uDCF8"}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h3",{className:"text-lg font-medium text-white mb-2",children:"رفع الصور"}),(0,s.jsxs)("p",{className:"text-gray-400 text-sm mb-4",children:["اسحب الصور هنا أو انقر للاختيار (الحد الأقصى ",c," صورة)"]}),(0,s.jsx)("button",{type:"button",onClick:()=>b.current?.click(),disabled:h||r.length>=c,className:"px-6 py-2 bg-primary text-white rounded-lg hover:bg-primary/90 disabled:opacity-50 disabled:cursor-not-allowed transition-colors",children:h?"جاري الرفع...":"اختيار الصور"})]}),(0,s.jsx)("div",{className:"text-xs text-gray-500",children:"الصيغ المدعومة: JPG, PNG, WebP, GIF (الحد الأقصى 5MB لكل صورة)"})]})]}),Object.keys(g).length>0&&(0,s.jsx)("div",{className:"space-y-2",children:Object.entries(g).map(([e,r])=>(0,s.jsxs)("div",{className:"bg-gray-800 rounded-lg p-3",children:[(0,s.jsxs)("div",{className:"flex justify-between text-sm text-gray-300 mb-2",children:[(0,s.jsx)("span",{children:"جاري الرفع..."}),(0,s.jsxs)("span",{children:[r,"%"]})]}),(0,s.jsx)("div",{className:"w-full bg-gray-700 rounded-full h-2",children:(0,s.jsx)("div",{className:"bg-primary h-2 rounded-full transition-all duration-300",style:{width:`${r}%`}})})]},e))}),r.length>0&&(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-white",children:["الصور المرفوعة (",r.length,")"]}),(0,s.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4",children:r.map((e,r)=>(0,s.jsxs)("div",{draggable:!0,onDragStart:()=>w(r),onDragOver:N,onDrop:e=>D(e,r),className:"bg-gray-800 rounded-lg overflow-hidden border border-gray-700 hover:border-primary transition-colors cursor-move",children:[(0,s.jsxs)("div",{className:"relative aspect-video bg-gray-900",children:[(0,s.jsx)("img",{src:e.image_url,alt:e.alt_text||"صورة المقال",className:"w-full h-full object-cover"}),(0,s.jsxs)("div",{className:"absolute top-2 right-2 flex space-x-2 space-x-reverse",children:[(0,s.jsx)("button",{type:"button",onClick:()=>$(e),className:"p-1 bg-primary text-white rounded hover:bg-primary/90 transition-colors",title:"إدراج في المحرر",children:"➕"}),(0,s.jsx)("button",{type:"button",onClick:()=>v(r),className:"p-1 bg-red-600 text-white rounded hover:bg-red-700 transition-colors",title:"حذف الصورة",children:"\uD83D\uDDD1️"})]}),(0,s.jsxs)("div",{className:"absolute bottom-2 left-2 bg-black/70 text-white text-xs px-2 py-1 rounded",children:["#",r+1]})]}),(0,s.jsxs)("div",{className:"p-4 space-y-3",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-300 mb-1",children:"النص البديل"}),(0,s.jsx)("input",{type:"text",value:e.alt_text||"",onChange:e=>U(r,e.target.value),className:"w-full px-2 py-1 text-sm bg-gray-700 border border-gray-600 rounded text-white focus:ring-1 focus:ring-primary focus:border-transparent",placeholder:"وصف الصورة"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block text-xs font-medium text-gray-300 mb-1",children:"التسمية التوضيحية"}),(0,s.jsx)("input",{type:"text",value:e.caption||"",onChange:e=>R(r,e.target.value),className:"w-full px-2 py-1 text-sm bg-gray-700 border border-gray-600 rounded text-white focus:ring-1 focus:ring-primary focus:border-transparent",placeholder:"تسمية توضيحية"})]}),(0,s.jsxs)("div",{className:"text-xs text-gray-400 space-y-1",children:[e.width&&e.height&&(0,s.jsxs)("div",{children:["الأبعاد: ",e.width," \xd7 ",e.height]}),e.file_size&&(0,s.jsxs)("div",{children:["الحجم: ",(e.file_size/1024/1024).toFixed(2)," MB"]})]})]})]},e.id))})]})]})}},41146:(e,r,t)=>{"use strict";function s(e,r=""){let t=Date.now(),l=(function(e="id"){return"undefined"!=typeof crypto&&crypto.randomUUID?`${e}-${crypto.randomUUID().substring(0,8)}`:`${e}-${Date.now()}-${Math.random().toString(36).substring(2,8)}`})("file").split("-")[1],i=e.split(".").pop()?.toLowerCase()||"jpg",a=`${t}-${l}.${i}`;return r?`${r}/${a}`:a}function l(){return new Date().toISOString()}function i(e,r=200){return Math.max(1,Math.ceil(e.trim().split(/\s+/).length/r))}t.d(r,{_C:()=>i,i0:()=>l,nw:()=>s})},42024:(e,r,t)=>{"use strict";t.d(r,{A:()=>i});var s=t(60687),l=t(43210);function i({value:e,onChange:r,placeholder:t="اكتب محتواك هنا...",className:i=""}){let a=(0,l.useRef)(null),[n,o]=(0,l.useState)(!1),c=(t,s="",l="")=>{let i=a.current;if(!i)return;let n=i.selectionStart,o=i.selectionEnd,c=e.substring(n,o)||l;r(e.substring(0,n)+t+c+s+e.substring(o)),setTimeout(()=>{let e=n+t.length+c.length;i.setSelectionRange(e,e),i.focus()},0)};return(0,s.jsxs)("div",{className:`border border-gray-700 rounded-lg ${i}`,children:[(0,s.jsxs)("div",{className:"flex items-center justify-between p-3 border-b border-gray-700 bg-gray-800/50",children:[(0,s.jsx)("div",{className:"flex items-center space-x-2 space-x-reverse",children:[{label:"نص عريض",action:()=>c("**","**","نص عريض"),icon:"B"},{label:"نص مائل",action:()=>c("*","*","نص مائل"),icon:"I"},{label:"رابط",action:()=>c("[","](https://example.com)","نص الرابط"),icon:"\uD83D\uDD17"},{label:"صورة",action:()=>c("![","](https://example.com/image.jpg)","وصف الصورة"),icon:"\uD83D\uDDBC️"},{label:"قائمة",action:()=>c("- ","","عنصر القائمة"),icon:"•"},{label:"كود",action:()=>c("`","`","كود"),icon:"</>"},{label:"عنوان",action:()=>c("## ","","العنوان"),icon:"H"},{label:"فيديو يوتيوب",action:()=>c("[youtube]","[/youtube]","https://www.youtube.com/watch?v=VIDEO_ID"),icon:"\uD83D\uDCF9"},{label:"معرض صور",action:()=>c("[gallery]","[/gallery]","grid,3,normal"),icon:"\uD83D\uDDBC️"}].map((e,r)=>(0,s.jsx)("button",{type:"button",onClick:e.action,className:"px-3 py-1 text-sm bg-gray-700 text-white rounded hover:bg-gray-600 transition-colors",title:e.label,children:e.icon},r))}),(0,s.jsx)("button",{type:"button",onClick:()=>o(!n),className:"text-sm text-gray-400 hover:text-white transition-colors",children:"مساعدة Markdown"})]}),n&&(0,s.jsx)("div",{className:"p-4 bg-gray-800/30 border-b border-gray-700 text-sm",children:(0,s.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-2",children:"التنسيق الأساسي:"}),(0,s.jsxs)("ul",{className:"space-y-1 text-gray-300",children:[(0,s.jsxs)("li",{children:[(0,s.jsx)("code",{children:"**نص عريض**"})," → ",(0,s.jsx)("strong",{children:"نص عريض"})]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("code",{children:"*نص مائل*"})," → ",(0,s.jsx)("em",{children:"نص مائل"})]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("code",{children:"`كود`"})," → ",(0,s.jsx)("code",{children:"كود"})]}),(0,s.jsxs)("li",{children:[(0,s.jsx)("code",{children:"~~نص مشطوب~~"})," → ",(0,s.jsx)("del",{children:"نص مشطوب"})]})]})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("h4",{className:"font-semibold text-white mb-2",children:"العناوين والروابط:"}),(0,s.jsxs)("ul",{className:"space-y-1 text-gray-300",children:[(0,s.jsx)("li",{children:(0,s.jsx)("code",{children:"# عنوان رئيسي"})}),(0,s.jsx)("li",{children:(0,s.jsx)("code",{children:"## عنوان فرعي"})}),(0,s.jsx)("li",{children:(0,s.jsx)("code",{children:"[نص الرابط](URL)"})}),(0,s.jsx)("li",{children:(0,s.jsx)("code",{children:"![وصف الصورة](URL)"})}),(0,s.jsx)("li",{children:(0,s.jsx)("code",{children:"[youtube]رابط_يوتيوب[/youtube]"})}),(0,s.jsx)("li",{children:(0,s.jsx)("code",{children:"[gallery]grid,3,normal[/gallery]"})})]})]})]})}),(0,s.jsx)("textarea",{ref:a,value:e,onChange:e=>r(e.target.value),placeholder:t,className:"w-full min-h-[400px] p-4 bg-transparent text-white resize-none focus:outline-none font-mono",style:{minHeight:"400px"}}),(0,s.jsxs)("div",{className:"flex justify-between items-center p-3 border-t border-gray-700 bg-gray-800/50 text-sm text-gray-400",children:[(0,s.jsxs)("span",{children:["عدد الكلمات: ",e.trim().split(/\s+/).filter(e=>e.length>0).length]}),(0,s.jsxs)("span",{children:["عدد الأحرف: ",e.length]})]})]})}},67083:(e,r,t)=>{"use strict";t.d(r,{ProtectedRoute:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call ProtectedRoute() from the server but ProtectedRoute is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\components\\ProtectedRoute.tsx","ProtectedRoute")},96182:(e,r,t)=>{Promise.resolve().then(t.bind(t,20769))},99111:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413),l=t(67083);function i({children:e}){return(0,s.jsx)(l.ProtectedRoute,{children:e})}}};