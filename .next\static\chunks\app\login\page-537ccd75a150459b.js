(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4520],{283:(e,t,s)=>{"use strict";s.d(t,{A:()=>o,AuthProvider:()=>l});var r=s(5155),a=s(2115),n=s(2099),u=s(5695);let i=(0,a.createContext)(void 0);function l(e){let{children:t}=e,[s,l]=(0,a.useState)(null),[o,d]=(0,a.useState)(null),[c,h]=(0,a.useState)(!0),m=(0,u.useRouter)();(0,a.useEffect)(()=>{(async()=>{var e;let{data:{session:t}}=await n.ND.auth.getSession();d(t),l(null!=(e=null==t?void 0:t.user)?e:null),h(!1)})();let{data:e}=n.ND.auth.onAuthStateChange((e,t)=>{var s;d(t),l(null!=(s=null==t?void 0:t.user)?s:null),h(!1)});return()=>{e.subscription.unsubscribe()}},[]);let b=async(e,t)=>{let{error:s}=await n.ND.auth.signInWithPassword({email:e,password:t});if(s)throw s;m.push("/admin")},p=async()=>{await n.ND.auth.signOut(),m.push("/login")};return(0,r.jsx)(i.Provider,{value:{session:o,user:s,loading:c,signIn:b,signOut:p},children:t})}function o(){let e=(0,a.useContext)(i);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}},2099:(e,t,s)=>{"use strict";s.d(t,{ND:()=>u});var r=s(5647);let a="https://zgktrwpladrkhhemhnni.supabase.co",n="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04";if(!a)throw Error("Missing NEXT_PUBLIC_SUPABASE_URL environment variable");if(!n)throw Error("Missing NEXT_PUBLIC_SUPABASE_ANON_KEY environment variable");let u=(0,r.UU)(a,n,{db:{schema:"public"},auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}})},3917:(e,t,s)=>{Promise.resolve().then(s.bind(s,9690))},5695:(e,t,s)=>{"use strict";var r=s(8999);s.o(r,"usePathname")&&s.d(t,{usePathname:function(){return r.usePathname}}),s.o(r,"useRouter")&&s.d(t,{useRouter:function(){return r.useRouter}})},9690:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>u});var r=s(5155),a=s(2115),n=s(283);function u(){let[e,t]=(0,a.useState)(""),[s,u]=(0,a.useState)(""),[i,l]=(0,a.useState)(null),[o,d]=(0,a.useState)(!1),{signIn:c}=(0,n.A)(),h=async t=>{t.preventDefault(),d(!0),l(null);try{await c(e,s)}catch(e){l("Invalid login credentials"===e.message?"البريد الإلكتروني أو كلمة المرور غير صحيحة.":e.message)}d(!1)};return(0,r.jsx)("div",{className:"flex justify-center items-center mt-16",children:(0,r.jsxs)("div",{className:"w-full max-w-md p-8 space-y-6 bg-dark-card rounded-lg shadow-lg",children:[(0,r.jsx)("h1",{className:"text-2xl font-bold text-center text-white",children:"تسجيل الدخول"}),(0,r.jsxs)("form",{onSubmit:h,className:"space-y-6",children:[(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block mb-2 text-sm font-medium text-dark-text-secondary",children:"البريد الإلكتروني"}),(0,r.jsx)("input",{type:"email",value:e,onChange:e=>t(e.target.value),required:!0,className:"w-full px-3 py-2 bg-[#0D1117] border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"})]}),(0,r.jsxs)("div",{children:[(0,r.jsx)("label",{className:"block mb-2 text-sm font-medium text-dark-text-secondary",children:"كلمة المرور"}),(0,r.jsx)("input",{type:"password",value:s,onChange:e=>u(e.target.value),required:!0,className:"w-full px-3 py-2 bg-[#0D1117] border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"})]}),i&&(0,r.jsx)("p",{className:"text-red-500 text-sm",children:i}),(0,r.jsx)("button",{type:"submit",disabled:o,className:"w-full px-4 py-2 font-bold text-white bg-primary rounded-md hover:bg-primary/90 disabled:bg-gray-500",children:o?"جاري الدخول...":"دخول"})]})]})})}}},e=>{var t=t=>e(e.s=t);e.O(0,[5647,8441,1684,7358],()=>t(3917)),_N_E=e.O()}]);