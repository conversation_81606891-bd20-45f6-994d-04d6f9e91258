/** @type {import('next').NextConfig} */
const nextConfig = {
  // إعدادات أساسية
  reactStrictMode: true,
  poweredByHeader: false,

  // إعدادات SSG و ISR - تحويل كامل للـ Static
  output: 'export',
  trailingSlash: true,

  // تعطيل الميزات التي لا تعمل مع Static Export
  images: {
    unoptimized: true,
  },

  // إعدادات compiler
  compiler: {
    removeConsole: process.env.NODE_ENV === 'production' ? {
      exclude: ['error']
    } : false,
  },
};

module.exports = nextConfig;
