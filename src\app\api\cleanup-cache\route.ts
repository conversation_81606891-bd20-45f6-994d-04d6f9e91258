import { NextResponse } from 'next/server';

/**
 * API لتنظيف Cache وتقليل استهلاك الذاكرة
 * يتم استدعاؤها كل 6 ساعات عبر Vercel Cron
 */
export async function GET() {
  try {
    // تنظيف Cache في الذاكرة
    if (global.gc) {
      global.gc();
    }

    // إرجاع استجابة بسيطة لتوفير Function Invocations
    return NextResponse.json({ 
      success: true, 
      timestamp: new Date().toISOString(),
      message: 'Cache cleaned successfully'
    }, {
      headers: {
        'Cache-Control': 'no-store, max-age=0'
      }
    });
  } catch (error) {
    console.error('Error cleaning cache:', error);
    return NextResponse.json({ 
      success: false, 
      error: 'Failed to clean cache' 
    }, { 
      status: 500,
      headers: {
        'Cache-Control': 'no-store, max-age=0'
      }
    });
  }
}
