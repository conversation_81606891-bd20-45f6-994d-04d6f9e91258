/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/test-ssg-build/route";
exports.ids = ["app/api/test-ssg-build/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-ssg-build%2Froute&page=%2Fapi%2Ftest-ssg-build%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-ssg-build%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-ssg-build%2Froute&page=%2Fapi%2Ftest-ssg-build%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-ssg-build%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var C_Users_ismail_4_src_app_api_test_ssg_build_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/test-ssg-build/route.ts */ \"(rsc)/./src/app/api/test-ssg-build/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"standalone\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/test-ssg-build/route\",\n        pathname: \"/api/test-ssg-build\",\n        filename: \"route\",\n        bundlePath: \"app/api/test-ssg-build/route\"\n    },\n    resolvedPagePath: \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\api\\\\test-ssg-build\\\\route.ts\",\n    nextConfigOutput,\n    userland: C_Users_ismail_4_src_app_api_test_ssg_build_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-ssg-build%2Froute&page=%2Fapi%2Ftest-ssg-build%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-ssg-build%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/test-ssg-build/route.ts":
/*!*********************************************!*\
  !*** ./src/app/api/test-ssg-build/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_ssg__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/ssg */ \"(rsc)/./src/lib/ssg.ts\");\n\n\nasync function GET() {\n    try {\n        console.log('🔄 Testing SSG build functions...');\n        const startTime = Date.now();\n        // اختبار جلب المقالات\n        const articlesStartTime = Date.now();\n        const articles = await (0,_lib_ssg__WEBPACK_IMPORTED_MODULE_1__.getAllArticlesForSSG)();\n        const articlesEndTime = Date.now();\n        // اختبار جلب أدوات AI\n        const aiToolsStartTime = Date.now();\n        const aiTools = await (0,_lib_ssg__WEBPACK_IMPORTED_MODULE_1__.getAllAIToolsForSSG)();\n        const aiToolsEndTime = Date.now();\n        const endTime = Date.now();\n        const result = {\n            success: true,\n            timestamp: new Date().toISOString(),\n            totalTime: endTime - startTime,\n            articles: {\n                count: articles.length,\n                time: articlesEndTime - articlesStartTime,\n                success: articles.length > 0,\n                sampleTitles: articles.slice(0, 3).map((a)=>a.title)\n            },\n            aiTools: {\n                count: aiTools.length,\n                time: aiToolsEndTime - aiToolsStartTime,\n                success: aiTools.length > 0,\n                sampleNames: aiTools.slice(0, 3).map((t)=>t.name)\n            },\n            environment: {\n                nodeEnv: \"development\",\n                hasSupabaseUrl: !!\"https://zgktrwpladrkhhemhnni.supabase.co\",\n                hasAnonKey: !!\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04\",\n                hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY\n            }\n        };\n        console.log('✅ SSG build test completed:', result);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(result);\n    } catch (error) {\n        console.error('💥 Exception in SSG build test:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            success: false,\n            error: error.message,\n            stack: error.stack,\n            timestamp: new Date().toISOString(),\n            environment: {\n                nodeEnv: \"development\",\n                hasSupabaseUrl: !!\"https://zgktrwpladrkhhemhnni.supabase.co\",\n                hasAnonKey: !!\"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04\",\n                hasServiceKey: !!process.env.SUPABASE_SERVICE_ROLE_KEY\n            }\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/test-ssg-build/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/ssg.ts":
/*!************************!*\
  !*** ./src/lib/ssg.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   fixObjectEncoding: () => (/* binding */ fixObjectEncoding),\n/* harmony export */   getAIToolBySlugForSSG: () => (/* binding */ getAIToolBySlugForSSG),\n/* harmony export */   getAdvertisementsForSSG: () => (/* binding */ getAdvertisementsForSSG),\n/* harmony export */   getAllAIToolsForSSG: () => (/* binding */ getAllAIToolsForSSG),\n/* harmony export */   getAllArticlesForSSG: () => (/* binding */ getAllArticlesForSSG),\n/* harmony export */   getArticleBySlugForSSG: () => (/* binding */ getArticleBySlugForSSG),\n/* harmony export */   getCategoriesForSSG: () => (/* binding */ getCategoriesForSSG),\n/* harmony export */   getStatsForSSG: () => (/* binding */ getStatsForSSG),\n/* harmony export */   supabaseSSG: () => (/* binding */ supabaseSSG)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/supabase-js */ \"(rsc)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n\n// استخدام service role key للبناء مع fallback للـ anon key\nconst supabaseUrl = \"https://zgktrwpladrkhhemhnni.supabase.co\";\nconst supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;\nconst supabaseAnonKey = \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpna3Ryd3BsYWRya2hoZW1obm5pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTIwMjk0NTIsImV4cCI6MjA2NzYwNTQ1Mn0.uHKisokqk484Vq5QjCbVbcdcabxArrtKUMxjdCihe04\";\n// التحقق من وجود المتغيرات المطلوبة\nif (!supabaseUrl) {\n    console.error('❌ NEXT_PUBLIC_SUPABASE_URL is not defined');\n}\nif (!supabaseServiceKey && !supabaseAnonKey) {\n    console.error('❌ Neither SUPABASE_SERVICE_ROLE_KEY nor NEXT_PUBLIC_SUPABASE_ANON_KEY is defined');\n}\n// استخدام service role key إذا كان متاحاً، وإلا استخدم anon key\nconst apiKey = supabaseServiceKey || supabaseAnonKey;\nconst supabaseSSG = supabaseUrl && apiKey ? (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_0__.createClient)(supabaseUrl, apiKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    }\n}) : null;\n/**\r\n * جلب جميع المقالات للـ SSG\r\n */ async function getAllArticlesForSSG() {\n    try {\n        // التحقق من وجود اتصال Supabase\n        if (!supabaseSSG) {\n            console.error('❌ Supabase SSG client is not initialized');\n            return [];\n        }\n        console.log('🔄 Fetching articles for SSG...');\n        const { data, error } = await supabaseSSG.from('articles').select(`\n        id,\n        title,\n        slug,\n        excerpt,\n        content,\n        featured_image,\n        author,\n        status,\n        tags,\n        meta_title,\n        meta_description,\n        reading_time,\n        views,\n        created_at,\n        updated_at\n      `).eq('status', 'published').order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            console.error('❌ Error fetching articles for SSG:', error);\n            return [];\n        }\n        console.log(`✅ Successfully fetched ${data?.length || 0} articles for SSG`);\n        return data || [];\n    } catch (error) {\n        console.error('💥 Exception in getAllArticlesForSSG:', error);\n        return [];\n    }\n}\n/**\r\n * جلب مقال واحد للـ SSG\r\n */ async function getArticleBySlugForSSG(slug) {\n    try {\n        // التحقق من وجود اتصال Supabase\n        if (!supabaseSSG) {\n            console.error('❌ Supabase SSG client is not initialized');\n            return null;\n        }\n        console.log(`🔄 Fetching article \"${slug}\" for SSG...`);\n        const { data, error } = await supabaseSSG.from('articles').select(`\n        id,\n        title,\n        slug,\n        excerpt,\n        content,\n        featured_image,\n        author,\n        status,\n        tags,\n        meta_title,\n        meta_description,\n        reading_time,\n        views,\n        created_at,\n        updated_at\n      `).eq('slug', slug).eq('status', 'published').maybeSingle(); // استخدام maybeSingle بدلاً من single لتجنب الأخطاء\n        if (error) {\n            console.error(`❌ Error fetching article \"${slug}\" for SSG:`, {\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            return null;\n        }\n        if (data) {\n            console.log(`✅ Successfully fetched article \"${slug}\" for SSG`);\n        } else {\n            console.log(`⚠️ Article \"${slug}\" not found in SSG`);\n        }\n        return data;\n    } catch (error) {\n        console.error(`💥 Exception in getArticleBySlugForSSG for \"${slug}\":`, {\n            message: error.message,\n            stack: error.stack\n        });\n        return null;\n    }\n}\n/**\r\n * جلب جميع أدوات AI للـ SSG\r\n */ async function getAllAIToolsForSSG() {\n    try {\n        // التحقق من وجود اتصال Supabase\n        if (!supabaseSSG) {\n            console.error('❌ Supabase SSG client is not initialized');\n            return [];\n        }\n        console.log('🔄 Fetching AI tools for SSG...');\n        const { data, error } = await supabaseSSG.from('ai_tools').select(`\n        id,\n        name,\n        slug,\n        description,\n        category,\n        pricing,\n        logo_url,\n        website_url,\n        features,\n        rating,\n        status,\n        meta_title,\n        meta_description,\n        created_at,\n        updated_at\n      `).in('status', [\n            'published',\n            'active'\n        ]).order('rating', {\n            ascending: false\n        });\n        if (error) {\n            console.error('❌ Error fetching AI tools for SSG:', {\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code\n            });\n            return [];\n        }\n        console.log(`✅ Successfully fetched ${data?.length || 0} AI tools for SSG`);\n        return data || [];\n    } catch (error) {\n        console.error('💥 Exception in getAllAIToolsForSSG:', {\n            message: error.message,\n            stack: error.stack\n        });\n        return [];\n    }\n}\n/**\r\n * جلب أداة AI واحدة للـ SSG\r\n */ async function getAIToolBySlugForSSG(slug) {\n    try {\n        // التحقق من أن supabaseSSG متاح\n        if (!supabaseSSG) {\n            console.error('supabaseSSG client is not available');\n            return null;\n        }\n        const { data, error } = await supabaseSSG.from('ai_tools').select(`\n        id,\n        name,\n        slug,\n        description,\n        category,\n        pricing,\n        logo_url,\n        website_url,\n        features,\n        rating,\n        status,\n        meta_title,\n        meta_description,\n        created_at,\n        updated_at\n      `).eq('slug', slug).eq('status', 'published').single();\n        if (error) {\n            console.error('Error fetching AI tool for SSG:', {\n                message: error.message,\n                details: error.details,\n                hint: error.hint,\n                code: error.code,\n                slug\n            });\n            return null;\n        }\n        return data;\n    } catch (error) {\n        console.error('Exception in getAIToolBySlugForSSG:', {\n            error: error instanceof Error ? error.message : String(error),\n            slug\n        });\n        return null;\n    }\n}\n/**\r\n * جلب الإعلانات للـ SSG\r\n */ async function getAdvertisementsForSSG(position) {\n    try {\n        if (!supabaseSSG) {\n            console.error('❌ Supabase SSG client is not initialized');\n            return [];\n        }\n        let query = supabaseSSG.from('advertisements').select(`\n        id,\n        title,\n        content,\n        type,\n        position,\n        is_active,\n        is_paused,\n        priority,\n        target_url,\n        image_url,\n        custom_css,\n        custom_js,\n        created_at,\n        updated_at\n      `).eq('is_active', true).eq('is_paused', false).order('priority', {\n            ascending: true\n        });\n        if (position) {\n            query = query.eq('position', position);\n        }\n        const { data, error } = await query;\n        if (error) {\n            console.error('Error fetching advertisements for SSG:', error);\n            return [];\n        }\n        return data || [];\n    } catch (error) {\n        console.error('Exception in getAdvertisementsForSSG:', error);\n        return [];\n    }\n}\n/**\r\n * جلب إحصائيات للـ SSG\r\n */ async function getStatsForSSG() {\n    try {\n        if (!supabaseSSG) {\n            console.error('❌ Supabase SSG client is not initialized');\n            return {\n                articles: 0,\n                aiTools: 0\n            };\n        }\n        const [articlesResult, aiToolsResult] = await Promise.all([\n            supabaseSSG.from('articles').select('id', {\n                count: 'exact'\n            }).eq('status', 'published'),\n            supabaseSSG.from('ai_tools').select('id', {\n                count: 'exact'\n            }).in('status', [\n                'published',\n                'active'\n            ])\n        ]);\n        return {\n            totalArticles: articlesResult.count || 0,\n            totalAITools: aiToolsResult.count || 0,\n            lastUpdated: new Date().toISOString()\n        };\n    } catch (error) {\n        console.error('Exception in getStatsForSSG:', error);\n        return {\n            totalArticles: 0,\n            totalAITools: 0,\n            lastUpdated: new Date().toISOString()\n        };\n    }\n}\n/**\r\n * جلب الفئات للـ SSG\r\n */ async function getCategoriesForSSG() {\n    try {\n        if (!supabaseSSG) {\n            console.error('❌ Supabase SSG client is not initialized');\n            return [];\n        }\n        const { data, error } = await supabaseSSG.from('ai_tools').select('category').in('status', [\n            'published',\n            'active'\n        ]);\n        if (error) {\n            console.error('Error fetching categories for SSG:', error);\n            return [];\n        }\n        // استخراج الفئات الفريدة\n        const uniqueCategories = Array.from(new Set(data?.map((tool)=>tool.category).filter(Boolean)));\n        return uniqueCategories;\n    } catch (error) {\n        console.error('Exception in getCategoriesForSSG:', error);\n        return [];\n    }\n}\n/**\r\n * تنظيف وإصلاح encoding النصوص العربية\r\n */ function fixObjectEncoding(obj) {\n    if (!obj) return obj;\n    if (typeof obj === 'string') {\n        return obj.replace(/Ø§/g, 'ا').replace(/Ù/g, 'ي').replace(/Ø¹/g, 'ع').replace(/Ù\\u0084/g, 'ل').replace(/Ø±/g, 'ر').replace(/Ø¨/g, 'ب').replace(/Ù\\u008A/g, 'ي').replace(/Ø©/g, 'ة').replace(/Ø§Ù\\u0084/g, 'ال').replace(/Ù\\u0085/g, 'م').replace(/Ù\\u0086/g, 'ن').replace(/Ø³/g, 'س').replace(/Ø¯/g, 'د').replace(/Ù\\u0081/g, 'ف').replace(/Ø­/g, 'ح').replace(/Ø¬/g, 'ج').replace(/Ù\\u0083/g, 'ك').replace(/Ø·/g, 'ط').replace(/Ù\\u0087/g, 'ه').replace(/Ø°/g, 'ذ').replace(/Ø²/g, 'ز').replace(/Ø´/g, 'ش').replace(/Ø¶/g, 'ض').replace(/Ø¸/g, 'ظ').replace(/Ø؛/g, 'غ').replace(/Ø®/g, 'خ').replace(/Ø«/g, 'ث').replace(/Ù\\u0082/g, 'ق').replace(/Ù\\u0088/g, 'و').replace(/Ø¡/g, 'ء').replace(/Ø¤/g, 'ؤ').replace(/Ø¦/g, 'ئ').replace(/Ø¥/g, 'إ').replace(/Ø¢/g, 'آ');\n    }\n    if (Array.isArray(obj)) {\n        return obj.map((item)=>fixObjectEncoding(item));\n    }\n    if (typeof obj === 'object' && obj !== null) {\n        const fixed = {};\n        for (const [key, value] of Object.entries(obj)){\n            fixed[key] = fixObjectEncoding(value);\n        }\n        return fixed;\n    }\n    return obj;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/ssg.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/tr46","vendor-chunks/ws","vendor-chunks/whatwg-url","vendor-chunks/webidl-conversions","vendor-chunks/isows"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Ftest-ssg-build%2Froute&page=%2Fapi%2Ftest-ssg-build%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Ftest-ssg-build%2Froute.ts&appDir=C%3A%5CUsers%5Cismail%5C4%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=C%3A%5CUsers%5Cismail%5C4&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=standalone&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();