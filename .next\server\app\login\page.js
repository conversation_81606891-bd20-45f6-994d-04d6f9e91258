(()=>{var e={};e.id=4520,e.ids=[4520],e.modules={3295:e=>{"use strict";e.exports=require("next/dist/server/app-render/after-task-async-storage.external.js")},10846:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},11997:e=>{"use strict";e.exports=require("punycode")},13485:(e,r,t)=>{"use strict";t.r(r),t.d(r,{GlobalError:()=>a.a,__next_app__:()=>p,pages:()=>u,routeModule:()=>c,tree:()=>d});var s=t(65239),i=t(48088),n=t(88170),a=t.n(n),o=t(30893),l={};for(let e in o)0>["default","tree","pages","GlobalError","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);t.d(r,l);let d={children:["",{children:["login",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,94934)),"C:\\Users\\<USER>\\4\\src\\app\\login\\page.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,67089)),"C:\\Users\\<USER>\\4\\src\\app\\login\\layout.tsx"]}]},{layout:[()=>Promise.resolve().then(t.bind(t,94431)),"C:\\Users\\<USER>\\4\\src\\app\\layout.tsx"],loading:[()=>Promise.resolve().then(t.bind(t,67393)),"C:\\Users\\<USER>\\4\\src\\app\\loading.tsx"],"not-found":[()=>Promise.resolve().then(t.bind(t,54413)),"C:\\Users\\<USER>\\4\\src\\app\\not-found.tsx"],forbidden:[()=>Promise.resolve().then(t.t.bind(t,89999,23)),"next/dist/client/components/forbidden-error"],unauthorized:[()=>Promise.resolve().then(t.t.bind(t,65284,23)),"next/dist/client/components/unauthorized-error"]}]}.children,u=["C:\\Users\\<USER>\\4\\src\\app\\login\\page.tsx"],p={require:t,loadChunk:()=>Promise.resolve()},c=new s.AppPageRouteModule({definition:{kind:i.RouteKind.APP_PAGE,page:"/login/page",pathname:"/login",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},19121:e=>{"use strict";e.exports=require("next/dist/server/app-render/action-async-storage.external.js")},27910:e=>{"use strict";e.exports=require("stream")},29294:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-async-storage.external.js")},33873:e=>{"use strict";e.exports=require("path")},34631:e=>{"use strict";e.exports=require("tls")},55511:e=>{"use strict";e.exports=require("crypto")},55591:e=>{"use strict";e.exports=require("https")},63033:e=>{"use strict";e.exports=require("next/dist/server/app-render/work-unit-async-storage.external.js")},65139:(e,r,t)=>{Promise.resolve().then(t.bind(t,69488))},67089:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>i});var s=t(37413);function i({children:e}){return(0,s.jsx)("div",{className:"min-h-screen bg-[#0D1117]",children:e})}},69488:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>a});var s=t(60687),i=t(43210),n=t(63213);function a(){let[e,r]=(0,i.useState)(""),[t,a]=(0,i.useState)(""),[o,l]=(0,i.useState)(null),[d,u]=(0,i.useState)(!1),{signIn:p}=(0,n.A)(),c=async r=>{r.preventDefault(),u(!0),l(null);try{await p(e,t)}catch(e){l("Invalid login credentials"===e.message?"البريد الإلكتروني أو كلمة المرور غير صحيحة.":e.message)}u(!1)};return(0,s.jsx)("div",{className:"flex justify-center items-center mt-16",children:(0,s.jsxs)("div",{className:"w-full max-w-md p-8 space-y-6 bg-dark-card rounded-lg shadow-lg",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-center text-white",children:"تسجيل الدخول"}),(0,s.jsxs)("form",{onSubmit:c,className:"space-y-6",children:[(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block mb-2 text-sm font-medium text-dark-text-secondary",children:"البريد الإلكتروني"}),(0,s.jsx)("input",{type:"email",value:e,onChange:e=>r(e.target.value),required:!0,className:"w-full px-3 py-2 bg-[#0D1117] border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"})]}),(0,s.jsxs)("div",{children:[(0,s.jsx)("label",{className:"block mb-2 text-sm font-medium text-dark-text-secondary",children:"كلمة المرور"}),(0,s.jsx)("input",{type:"password",value:t,onChange:e=>a(e.target.value),required:!0,className:"w-full px-3 py-2 bg-[#0D1117] border border-gray-600 rounded-md focus:outline-none focus:ring-2 focus:ring-primary"})]}),o&&(0,s.jsx)("p",{className:"text-red-500 text-sm",children:o}),(0,s.jsx)("button",{type:"submit",disabled:d,className:"w-full px-4 py-2 font-bold text-white bg-primary rounded-md hover:bg-primary/90 disabled:bg-gray-500",children:d?"جاري الدخول...":"دخول"})]})]})})}},74075:e=>{"use strict";e.exports=require("zlib")},79428:e=>{"use strict";e.exports=require("buffer")},79551:e=>{"use strict";e.exports=require("url")},81630:e=>{"use strict";e.exports=require("http")},88699:(e,r,t)=>{Promise.resolve().then(t.bind(t,94934))},91645:e=>{"use strict";e.exports=require("net")},94735:e=>{"use strict";e.exports=require("events")},94934:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>s});let s=(0,t(12907).registerClientReference)(function(){throw Error("Attempted to call the default export of \"C:\\\\Users\\\\<USER>\\\\4\\\\src\\\\app\\\\login\\\\page.tsx\" from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"C:\\Users\\<USER>\\4\\src\\app\\login\\page.tsx","default")}};var r=require("../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[2714,6656],()=>t(13485));module.exports=s})();