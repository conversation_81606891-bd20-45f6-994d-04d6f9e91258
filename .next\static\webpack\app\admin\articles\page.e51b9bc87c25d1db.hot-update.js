"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/admin/articles/page",{

/***/ "(app-pages-browser)/./src/lib/database.ts":
/*!*****************************!*\
  !*** ./src/lib/database.ts ***!
  \*****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createAITool: () => (/* binding */ createAITool),\n/* harmony export */   createArticle: () => (/* binding */ createArticle),\n/* harmony export */   createService: () => (/* binding */ createService),\n/* harmony export */   deleteAITool: () => (/* binding */ deleteAITool),\n/* harmony export */   deleteArticle: () => (/* binding */ deleteArticle),\n/* harmony export */   deleteService: () => (/* binding */ deleteService),\n/* harmony export */   getAIToolById: () => (/* binding */ getAIToolById),\n/* harmony export */   getAITools: () => (/* binding */ getAITools),\n/* harmony export */   getArticleById: () => (/* binding */ getArticleById),\n/* harmony export */   getArticles: () => (/* binding */ getArticles),\n/* harmony export */   getDashboardStats: () => (/* binding */ getDashboardStats),\n/* harmony export */   getServiceById: () => (/* binding */ getServiceById),\n/* harmony export */   getServices: () => (/* binding */ getServices),\n/* harmony export */   updateAITool: () => (/* binding */ updateAITool),\n/* harmony export */   updateArticle: () => (/* binding */ updateArticle),\n/* harmony export */   updateService: () => (/* binding */ updateService)\n/* harmony export */ });\n/* harmony import */ var _supabase__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! ./supabase */ \"(app-pages-browser)/./src/lib/supabase.ts\");\n/* harmony import */ var _cache__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./cache */ \"(app-pages-browser)/./src/lib/cache.ts\");\n// مساعدات قاعدة البيانات لعمليات CRUD\n\n\n// ===== مساعدات المقالات =====\n// جلب جميع المقالات مع التخزين المؤقت - محسن لتوفير Egress\nasync function getArticles() {\n    return (0,_cache__WEBPACK_IMPORTED_MODULE_1__.cachedQuery)('articles-all', async ()=>{\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').select(\"\\n        id,\\n        title,\\n        slug,\\n        excerpt,\\n        featured_image_url,\\n        published_at,\\n        created_at,\\n        status\\n      \").eq('status', 'published').order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            throw new Error(\"خطأ في جلب المقالات: \".concat(error.message));\n        }\n        return data;\n    }, 1800); // 30 دقيقة بدلاً من 5 دقائق\n}\n// جلب مقال واحد بالـ ID\nasync function getArticleById(id) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').select('*').eq('id', id).single();\n    if (error) {\n        throw new Error(\"خطأ في جلب المقال: \".concat(error.message));\n    }\n    return data;\n}\n// إنشاء مقال جديد\nasync function createArticle(articleData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').insert([\n        {\n            ...articleData,\n            published_at: articleData.status === 'published' ? new Date().toISOString() : null\n        }\n    ]).select().single();\n    if (error) {\n        throw new Error(\"خطأ في إنشاء المقال: \".concat(error.message));\n    }\n    return data;\n}\n// تحديث مقال\nasync function updateArticle(id, articleData) {\n    const updateData = {\n        ...articleData,\n        updated_at: new Date().toISOString()\n    };\n    // إذا تم تغيير الحالة إلى منشور، تحديث تاريخ النشر\n    if (articleData.status === 'published') {\n        updateData.published_at = new Date().toISOString();\n    } else if (articleData.status === 'draft') {\n        updateData.published_at = null;\n    }\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').update(updateData).eq('id', id).select().single();\n    if (error) {\n        throw new Error(\"خطأ في تحديث المقال: \".concat(error.message));\n    }\n    return data;\n}\n// حذف مقال\nasync function deleteArticle(id) {\n    const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').delete().eq('id', id);\n    if (error) {\n        throw new Error(\"خطأ في حذف المقال: \".concat(error.message));\n    }\n}\n// ===== مساعدات أدوات الذكاء الاصطناعي =====\n// جلب جميع أدوات الذكاء الاصطناعي - محسن لتوفير Egress\nasync function getAITools() {\n    return (0,_cache__WEBPACK_IMPORTED_MODULE_1__.cachedQuery)('ai-tools-all', async ()=>{\n        const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').select(\"\\n        id,\\n        name,\\n        slug,\\n        description,\\n        category,\\n        website_url,\\n        logo_url,\\n        pricing,\\n        rating,\\n        features,\\n        status,\\n        featured,\\n        created_at\\n      \").in('status', [\n            'published',\n            'active'\n        ]).order('rating', {\n            ascending: false\n        }).order('created_at', {\n            ascending: false\n        });\n        if (error) {\n            throw new Error(\"خطأ في جلب أدوات الذكاء الاصطناعي: \".concat(error.message));\n        }\n        return data;\n    }, 1800); // 30 دقيقة\n}\n// جلب أداة ذكاء اصطناعي واحدة بالـ ID\nasync function getAIToolById(id) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').select('*').eq('id', id).single();\n    if (error) {\n        throw new Error(\"خطأ في جلب أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n    return data;\n}\n// إنشاء أداة ذكاء اصطناعي جديدة\nasync function createAITool(toolData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').insert([\n        toolData\n    ]).select().single();\n    if (error) {\n        throw new Error(\"خطأ في إنشاء أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n    return data;\n}\n// تحديث أداة ذكاء اصطناعي\nasync function updateAITool(id, toolData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').update({\n        ...toolData,\n        updated_at: new Date().toISOString()\n    }).eq('id', id).select().single();\n    if (error) {\n        throw new Error(\"خطأ في تحديث أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n    return data;\n}\n// حذف أداة ذكاء اصطناعي\nasync function deleteAITool(id) {\n    const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').delete().eq('id', id);\n    if (error) {\n        throw new Error(\"خطأ في حذف أداة الذكاء الاصطناعي: \".concat(error.message));\n    }\n}\n// ===== مساعدات الخدمات =====\n// جلب جميع الخدمات\nasync function getServices() {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').select('*').order('created_at', {\n        ascending: false\n    });\n    if (error) {\n        throw new Error(\"خطأ في جلب الخدمات: \".concat(error.message));\n    }\n    return data;\n}\n// جلب خدمة واحدة بالـ ID\nasync function getServiceById(id) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').select('*').eq('id', id).single();\n    if (error) {\n        throw new Error(\"خطأ في جلب الخدمة: \".concat(error.message));\n    }\n    return data;\n}\n// إنشاء خدمة جديدة\nasync function createService(serviceData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').insert([\n        serviceData\n    ]).select().single();\n    if (error) {\n        throw new Error(\"خطأ في إنشاء الخدمة: \".concat(error.message));\n    }\n    return data;\n}\n// تحديث خدمة\nasync function updateService(id, serviceData) {\n    const { data, error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').update({\n        ...serviceData,\n        updated_at: new Date().toISOString()\n    }).eq('id', id).select().single();\n    if (error) {\n        throw new Error(\"خطأ في تحديث الخدمة: \".concat(error.message));\n    }\n    return data;\n}\n// حذف خدمة\nasync function deleteService(id) {\n    const { error } = await _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').delete().eq('id', id);\n    if (error) {\n        throw new Error(\"خطأ في حذف الخدمة: \".concat(error.message));\n    }\n}\n// ===== مساعدات الإحصائيات =====\n// جلب إحصائيات سريعة للوحة التحكم\nasync function getDashboardStats() {\n    try {\n        var _articlesResult_data, _articlesResult_data1;\n        const [articlesResult, aiToolsResult, servicesResult] = await Promise.all([\n            _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('articles').select('status', {\n                count: 'exact'\n            }),\n            _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('ai_tools').select('status', {\n                count: 'exact'\n            }),\n            _supabase__WEBPACK_IMPORTED_MODULE_0__.supabase.from('services').select('status', {\n                count: 'exact'\n            })\n        ]);\n        const articlesCount = articlesResult.count || 0;\n        const publishedArticles = ((_articlesResult_data = articlesResult.data) === null || _articlesResult_data === void 0 ? void 0 : _articlesResult_data.filter((a)=>a.status === 'published').length) || 0;\n        const draftArticles = ((_articlesResult_data1 = articlesResult.data) === null || _articlesResult_data1 === void 0 ? void 0 : _articlesResult_data1.filter((a)=>a.status === 'draft').length) || 0;\n        const aiToolsCount = aiToolsResult.count || 0;\n        const servicesCount = servicesResult.count || 0;\n        return {\n            articles: {\n                total: articlesCount,\n                published: publishedArticles,\n                drafts: draftArticles\n            },\n            aiTools: aiToolsCount,\n            services: servicesCount\n        };\n    } catch (error) {\n        throw new Error(\"خطأ في جلب الإحصائيات: \".concat(error.message));\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/database.ts\n"));

/***/ })

});